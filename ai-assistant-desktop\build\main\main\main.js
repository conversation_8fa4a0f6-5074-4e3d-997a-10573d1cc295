"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const DatabaseService_1 = require("./database/DatabaseService");
const SettingsService_1 = require("./services/SettingsService");
const LLMService_1 = require("./services/LLMService");
const ToolRegistry_1 = require("./services/ToolRegistry");
const AgentService_1 = require("./services/AgentService");
const FileSystemService_1 = require("./services/FileSystemService");
const SystemService_1 = require("./services/SystemService");
const index_1 = require("@shared/constants/index");
class MainApp {
    constructor() {
        this.mainWindow = null;
        this.databaseService = new DatabaseService_1.DatabaseService();
        this.settingsService = new SettingsService_1.SettingsService(this.databaseService);
        this.llmService = new LLMService_1.LLMService();
        this.toolRegistry = new ToolRegistry_1.ToolRegistry();
        this.agentService = new AgentService_1.AgentService(this.llmService, this.toolRegistry);
        this.fileSystemService = new FileSystemService_1.FileSystemService();
        this.systemService = new SystemService_1.SystemService();
    }
    async initialize() {
        await this.databaseService.initialize();
        await this.settingsService.initialize();
        await this.toolRegistry.initialize();
        this.setupIPC();
    }
    createWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js'),
                webSecurity: true,
            },
            titleBarStyle: 'hiddenInset',
            frame: false,
            show: false,
            backgroundColor: '#18181b',
        });
        // Load the app
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
        }
        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            if (this.mainWindow) {
                this.mainWindow.show();
                this.mainWindow.focus();
            }
        });
        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        // Handle external links
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            electron_1.shell.openExternal(url);
            return { action: 'deny' };
        });
    }
    setupIPC() {
        // LLM Service
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.LLM_CHAT, async (event, request) => {
            try {
                const settings = await this.settingsService.getSettings();
                return await this.llmService.chat(request, settings.llm);
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.LLM_STREAM, async (event, request) => {
            try {
                const settings = await this.settingsService.getSettings();
                return await this.llmService.streamChat(request, settings.llm);
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // Agent Service
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.AGENT_PLAN, async (event, request) => {
            try {
                return await this.agentService.createPlan(request);
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.AGENT_EXECUTE, async (event, request) => {
            try {
                return await this.agentService.executePlan(request);
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // Tool Registry
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.TOOL_LIST, async () => {
            try {
                return { success: true, data: this.toolRegistry.getAvailableTools() };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.TOOL_EXECUTE, async (event, request) => {
            try {
                return await this.toolRegistry.executeTool(request.name, request.arguments);
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // Settings Service
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.SETTINGS_GET, async () => {
            try {
                const settings = await this.settingsService.getSettings();
                return { success: true, data: settings };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.SETTINGS_SET, async (event, request) => {
            try {
                await this.settingsService.updateSettings(request);
                return { success: true };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // File System Service
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.FS_READ, async (event, request) => {
            try {
                const content = await this.fileSystemService.readFile(request.path);
                return { success: true, data: content };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.FS_WRITE, async (event, request) => {
            try {
                await this.fileSystemService.writeFile(request.path, request.content);
                return { success: true };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.FS_LIST, async (event, request) => {
            try {
                const files = await this.fileSystemService.listDirectory(request.path);
                return { success: true, data: files };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // System Service
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.SYSTEM_INFO, async () => {
            try {
                const info = await this.systemService.getSystemInfo();
                return { success: true, data: info };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.SYSTEM_SHELL, async (event, request) => {
            try {
                const result = await this.systemService.executeCommand(request.command, request.options);
                return { success: true, data: result };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // Conversation Management
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.CONVERSATION_CREATE, async (event, request) => {
            try {
                const conversation = await this.databaseService.createConversation(request);
                return { success: true, data: conversation };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.CONVERSATION_LIST, async () => {
            try {
                const conversations = await this.databaseService.getConversations();
                return { success: true, data: conversations };
            }
            catch (error) {
                return { success: false, error: error instanceof Error ? error.message : String(error) };
            }
        });
        // Window Management
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.WINDOW_MINIMIZE, () => {
            this.mainWindow?.minimize();
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.WINDOW_MAXIMIZE, () => {
            if (this.mainWindow?.isMaximized()) {
                this.mainWindow.unmaximize();
            }
            else {
                this.mainWindow?.maximize();
            }
        });
        electron_1.ipcMain.handle(index_1.IPC_CHANNELS.WINDOW_CLOSE, () => {
            this.mainWindow?.close();
        });
    }
    async run() {
        // Handle app events
        electron_1.app.whenReady().then(async () => {
            await this.initialize();
            this.createWindow();
            electron_1.app.on('activate', () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createWindow();
                }
            });
        });
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('before-quit', async () => {
            await this.databaseService.close();
        });
        // Security: Prevent new window creation
        electron_1.app.on('web-contents-created', (event, contents) => {
            contents.setWindowOpenHandler(({ url }) => {
                electron_1.shell.openExternal(url);
                return { action: 'deny' };
            });
        });
    }
}
// Create and run the app
const mainApp = new MainApp();
mainApp.run().catch(console.error);
//# sourceMappingURL=main.js.map