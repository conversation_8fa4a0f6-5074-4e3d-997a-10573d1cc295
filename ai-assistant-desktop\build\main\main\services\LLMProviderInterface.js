"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseLLMProvider = void 0;
class BaseLLMProvider {
    async initialize(config) {
        if (!this.validateConfig(config)) {
            throw new Error(`Invalid configuration for ${this.name}`);
        }
        this.config = config;
    }
    validateConfig(config) {
        return !!(config.apiKey &&
            config.model &&
            this.supportedModels.includes(config.model));
    }
    buildHeaders(config) {
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`,
        };
    }
    formatMessages(messages, systemPrompt) {
        const formatted = messages.map(msg => ({
            role: msg.role,
            content: msg.content,
        }));
        if (systemPrompt) {
            formatted.unshift({
                role: 'system',
                content: systemPrompt,
            });
        }
        return formatted;
    }
}
exports.BaseLLMProvider = BaseLLMProvider;
//# sourceMappingURL=LLMProviderInterface.js.map