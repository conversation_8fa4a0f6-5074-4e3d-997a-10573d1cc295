"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentService = void 0;
const uuid_1 = require("uuid");
const index_1 = require("@shared/constants/index");
class AgentService {
    constructor(llmService, toolRegistry) {
        this.llmService = llmService;
        this.toolRegistry = toolRegistry;
        this.conversations = new Map();
        this.currentState = {
            status: 'idle',
            lastUpdate: new Date(),
            toolResults: [],
            isWaitingForConfirmation: false,
            executionMode: 'confirm',
        };
    }
    // Listen: Receive user prompt and prepare context
    async listen(input) {
        const { conversationId, message, context } = input;
        // Create user message
        const userMessage = {
            id: (0, uuid_1.v4)(),
            role: 'user',
            content: message,
            timestamp: new Date(),
            metadata: { context },
        };
        // Get or create conversation
        if (!this.conversations.has(conversationId)) {
            this.conversations.set(conversationId, []);
        }
        const conversation = this.conversations.get(conversationId);
        conversation.push(userMessage);
        return userMessage;
    }
    // Understand & Plan: Process user input and create execution plan
    async createPlan(request) {
        const { conversationId, executionMode = 'confirm', maxSteps = 10 } = request;
        try {
            const conversation = this.conversations.get(conversationId);
            if (!conversation || conversation.length === 0) {
                throw new Error('No conversation found');
            }
            // Update execution mode
            this.currentState.executionMode = executionMode;
            // Build system prompt with available tools
            const toolsPrompt = this.toolRegistry.generateSystemPrompt();
            const basePrompt = executionMode === 'yolo'
                ? index_1.SYSTEM_PROMPTS.YOLO_MODE
                : index_1.SYSTEM_PROMPTS.DEFAULT;
            const systemPrompt = `${basePrompt}\n\n${toolsPrompt}\n\n` +
                `Your task is to analyze the user's request and create a detailed execution plan. ` +
                `Break down the task into specific, actionable steps using the available tools.\n\n` +
                `Respond with a JSON object containing:\n` +
                `{\n` +
                `  "steps": [\n` +
                `    {\n` +
                `      "id": "unique_step_id",\n` +
                `      "description": "What this step will do",\n` +
                `      "tool": "tool_name",\n` +
                `      "arguments": { "param": "value" },\n` +
                `      "depends_on": ["previous_step_id"] // optional\n` +
                `    }\n` +
                `  ],\n` +
                `  "requires_confirmation": ${executionMode === 'confirm'},\n` +
                `  "estimated_duration": 30000 // milliseconds\n` +
                `}\n\n` +
                `Only include steps that are necessary to complete the user's request. ` +
                `Be specific about tool arguments and ensure they match the tool schemas.`;
            // Get latest messages for context
            const recentMessages = conversation.slice(-10); // Last 10 messages for context
            // Request plan from LLM
            const llmConfig = {
                provider: 'openai', // This should come from settings
                model: 'gpt-4',
                apiKey: process.env.OPENAI_API_KEY || '',
                maxTokens: 2048,
                temperature: 0.3, // Lower temperature for more consistent planning
            };
            const planResponse = await this.llmService.chat({ messages: recentMessages, systemPrompt }, llmConfig);
            // Parse the plan from LLM response
            const plan = this.parsePlanFromResponse(planResponse.content);
            if (!plan) {
                throw new Error('Failed to generate a valid execution plan');
            }
            // Validate plan steps
            const validationErrors = this.validatePlan(plan);
            if (validationErrors.length > 0) {
                throw new Error(`Plan validation failed: ${validationErrors.join(', ')}`);
            }
            // Store plan in current state
            this.currentPlan = plan;
            this.currentState.isWaitingForConfirmation = plan.requires_confirmation || false;
            return { success: true, plan };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    // Confirm: Wait for user confirmation if needed
    async confirmExecution(request) {
        const { conversationId, approved, modifiedPlan } = request;
        if (!this.currentState.isWaitingForConfirmation) {
            return { success: false, message: 'No plan waiting for confirmation' };
        }
        if (!approved) {
            // User rejected the plan
            this.currentPlan = undefined;
            this.currentState.isWaitingForConfirmation = false;
            return { success: true, message: 'Execution cancelled by user' };
        }
        // User approved, optionally with modifications
        if (modifiedPlan) {
            const validationErrors = this.validatePlan(modifiedPlan);
            if (validationErrors.length > 0) {
                return {
                    success: false,
                    message: `Modified plan validation failed: ${validationErrors.join(', ')}`
                };
            }
            this.currentPlan = modifiedPlan;
        }
        this.currentState.isWaitingForConfirmation = false;
        return { success: true, message: 'Execution approved' };
    }
    // Execute: Run the approved plan
    async executePlan(request) {
        const { conversationId, stepId } = request;
        const results = [];
        try {
            if (!this.currentPlan) {
                throw new Error('No plan available for execution');
            }
            if (this.currentState.isWaitingForConfirmation) {
                throw new Error('Plan requires confirmation before execution');
            }
            const plan = this.currentPlan;
            let startStepIndex = 0;
            // Find starting step
            if (stepId) {
                startStepIndex = plan.steps.findIndex(step => step.id === stepId);
                if (startStepIndex === -1) {
                    throw new Error(`Step not found: ${stepId}`);
                }
            }
            else if (this.executingStep) {
                // Continue from last executing step
                const lastIndex = plan.steps.findIndex(step => step.id === this.executingStep);
                startStepIndex = lastIndex + 1;
            }
            let currentStepIndex = startStepIndex;
            // Execute steps
            while (currentStepIndex < plan.steps.length) {
                const step = plan.steps[currentStepIndex];
                // Check if step dependencies are satisfied
                if (!this.areDependenciesSatisfied(step, this.currentState.toolResults)) {
                    throw new Error(`Dependencies not satisfied for step: ${step.id}`);
                }
                this.executingStep = step.id;
                try {
                    // Execute the tool
                    if (!step.tool) {
                        throw new Error(`Step ${step.id} is missing tool name`);
                    }
                    const toolResult = await this.toolRegistry.executeTool(step.tool, step.arguments || {});
                    toolResult.metadata = {
                        ...toolResult.metadata,
                        stepId: step.id,
                        stepDescription: step.description,
                    };
                    results.push(toolResult);
                    this.currentState.toolResults.push(toolResult);
                    // Check if tool execution failed
                    if (!toolResult.success) {
                        // In confirm mode, stop on first error
                        if (this.currentState.executionMode === 'confirm') {
                            throw new Error(`Tool execution failed: ${toolResult.error}`);
                        }
                        // In YOLO mode, continue with warning
                        console.warn(`Tool execution warning: ${toolResult.error}`);
                    }
                }
                catch (error) {
                    const failedResult = {
                        id: (0, uuid_1.v4)(),
                        toolName: step.tool || 'unknown',
                        name: step.tool,
                        input: step.arguments || {},
                        output: null,
                        result: null,
                        status: 'error',
                        success: false,
                        error: error.message,
                        timestamp: new Date(),
                        metadata: {
                            stepId: step.id,
                            stepDescription: step.description,
                            timestamp: new Date().toISOString(),
                        },
                    };
                    results.push(failedResult);
                    this.currentState.toolResults.push(failedResult);
                    throw error;
                }
                currentStepIndex++;
                // Break after each step in confirm mode for user observation
                if (this.currentState.executionMode === 'confirm') {
                    const isLastStep = currentStepIndex >= plan.steps.length;
                    return {
                        success: true,
                        results,
                        nextStep: isLastStep ? undefined : plan.steps[currentStepIndex]?.id,
                        completed: isLastStep,
                    };
                }
            }
            // All steps completed
            this.executingStep = undefined;
            this.currentPlan = undefined;
            return {
                success: true,
                results,
                completed: true,
            };
        }
        catch (error) {
            this.executingStep = undefined;
            return {
                success: false,
                error: error.message,
                results,
            };
        }
    }
    // Observe: Generate response based on execution results
    async observe(request) {
        const { conversationId, results, userQuestion } = request;
        try {
            const conversation = this.conversations.get(conversationId);
            if (!conversation) {
                throw new Error('Conversation not found');
            }
            // Build context from tool results
            const resultsContext = results
                .map(result => {
                const status = result.success ? '✅' : '❌';
                const output = result.success
                    ? JSON.stringify(result.result, null, 2)
                    : `Error: ${result.error}`;
                return `${status} ${result.name}${result.metadata?.stepDescription ? ` (${result.metadata.stepDescription})` : ''}\n${output}`;
            })
                .join('\n\n');
            const systemPrompt = `You are an AI assistant. Based on the tool execution results below, provide a helpful response to the user.

Tool Execution Results:
${resultsContext}

${userQuestion ? `User's follow-up question: ${userQuestion}` : ''}

Guidelines:
- Summarize what was accomplished
- Highlight any important results or findings
- If there were errors, explain what went wrong and suggest next steps
- Be concise but informative
- Use a friendly, professional tone`;
            // Get LLM response
            const llmConfig = {
                provider: 'openai',
                model: 'gpt-4',
                apiKey: process.env.OPENAI_API_KEY || '',
                maxTokens: 1024,
                temperature: 0.7,
            };
            const response = await this.llmService.chat({
                messages: conversation.slice(-5), // Recent context
                systemPrompt
            }, llmConfig);
            // Add assistant response to conversation
            const assistantMessage = {
                id: (0, uuid_1.v4)(),
                role: 'assistant',
                content: response.content,
                timestamp: new Date(),
                metadata: { toolResults: results },
            };
            conversation.push(assistantMessage);
            return {
                success: true,
                response: response.content,
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
            };
        }
    }
    // Utility methods
    parsePlanFromResponse(response) {
        try {
            // Extract JSON from response (might be wrapped in markdown)
            const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                response.match(/(\{[\s\S]*\})/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            const planData = JSON.parse(jsonMatch[1]);
            return {
                id: (0, uuid_1.v4)(),
                description: planData.description || 'Generated plan',
                riskLevel: planData.riskLevel || 'medium',
                steps: planData.steps.map((step) => ({
                    ...step,
                    id: step.id || (0, uuid_1.v4)(),
                })),
                estimatedDuration: planData.estimated_duration,
                estimated_duration: planData.estimated_duration,
                requires_confirmation: planData.requires_confirmation,
                createdAt: new Date(),
            };
        }
        catch (error) {
            console.error('Failed to parse plan:', error);
            return null;
        }
    }
    validatePlan(plan) {
        const errors = [];
        if (!plan.steps || plan.steps.length === 0) {
            errors.push('Plan must contain at least one step');
            return errors;
        }
        const stepIds = new Set();
        for (const step of plan.steps) {
            // Check for duplicate IDs
            if (stepIds.has(step.id)) {
                errors.push(`Duplicate step ID: ${step.id}`);
            }
            stepIds.add(step.id);
            // Validate tool exists
            if (!step.tool) {
                errors.push(`Step ${step.id} is missing tool name`);
                continue;
            }
            if (!this.toolRegistry.isToolAvailable(step.tool)) {
                errors.push(`Unknown tool: ${step.tool}`);
                continue;
            }
            // Validate tool arguments
            const toolSchema = this.toolRegistry.getToolSchema(step.tool);
            if (toolSchema && toolSchema.parameters.required) {
                const requiredParams = toolSchema.parameters.required;
                for (const paramName of requiredParams) {
                    if (!step.arguments || !(paramName in step.arguments)) {
                        errors.push(`Missing required parameter '${paramName}' for tool '${step.tool}' in step '${step.id}'`);
                    }
                }
            }
            // Validate dependencies
            if (step.depends_on) {
                for (const depId of step.depends_on) {
                    if (!stepIds.has(depId)) {
                        errors.push(`Step '${step.id}' depends on non-existent step '${depId}'`);
                    }
                }
            }
        }
        return errors;
    }
    areDependenciesSatisfied(step, toolResults) {
        if (!step.depends_on || step.depends_on.length === 0) {
            return true;
        }
        const completedSteps = new Set(toolResults
            .filter(result => result.success && result.metadata?.stepId)
            .map(result => result.metadata.stepId));
        return step.depends_on.every(depId => completedSteps.has(depId));
    }
    // Public API methods
    getCurrentState() {
        return { ...this.currentState };
    }
    getConversation(conversationId) {
        return this.conversations.get(conversationId);
    }
    clearState() {
        this.currentState = {
            status: 'idle',
            toolResults: [],
            isWaitingForConfirmation: false,
            executionMode: 'confirm',
            lastUpdate: new Date(),
        };
    }
    setExecutionMode(mode) {
        this.currentState.executionMode = mode;
    }
}
exports.AgentService = AgentService;
//# sourceMappingURL=AgentService.js.map