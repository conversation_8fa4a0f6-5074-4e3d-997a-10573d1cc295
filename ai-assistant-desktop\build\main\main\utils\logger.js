"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitor = exports.asyncErrorHandler = exports.handleError = exports.NetworkError = exports.RateLimitError = exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AppError = exports.logger = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
class Logger {
    constructor() {
        this.maxLogSize = 10 * 1024 * 1024; // 10MB
        this.maxLogFiles = 5;
        this.logDir = path.join(electron_1.app.getPath('userData'), 'logs');
        this.logFile = path.join(this.logDir, 'app.log');
        this.ensureLogDirectory();
    }
    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }
    formatLogEntry(level, message, data, stack) {
        const entry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            data,
            stack,
        };
        return JSON.stringify(entry) + '\n';
    }
    writeToFile(content) {
        try {
            // Check if log rotation is needed
            if (fs.existsSync(this.logFile)) {
                const stats = fs.statSync(this.logFile);
                if (stats.size > this.maxLogSize) {
                    this.rotateLogFiles();
                }
            }
            fs.appendFileSync(this.logFile, content);
        }
        catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    rotateLogFiles() {
        try {
            // Move existing log files
            for (let i = this.maxLogFiles - 1; i > 0; i--) {
                const oldFile = path.join(this.logDir, `app.log.${i}`);
                const newFile = path.join(this.logDir, `app.log.${i + 1}`);
                if (fs.existsSync(oldFile)) {
                    if (i === this.maxLogFiles - 1) {
                        fs.unlinkSync(oldFile); // Delete oldest log
                    }
                    else {
                        fs.renameSync(oldFile, newFile);
                    }
                }
            }
            // Move current log to .1
            if (fs.existsSync(this.logFile)) {
                fs.renameSync(this.logFile, path.join(this.logDir, 'app.log.1'));
            }
        }
        catch (error) {
            console.error('Failed to rotate log files:', error);
        }
    }
    debug(message, data) {
        const logEntry = this.formatLogEntry('debug', message, data);
        console.debug(`[DEBUG] ${message}`, data);
        this.writeToFile(logEntry);
    }
    info(message, data) {
        const logEntry = this.formatLogEntry('info', message, data);
        console.info(`[INFO] ${message}`, data);
        this.writeToFile(logEntry);
    }
    warn(message, data) {
        const logEntry = this.formatLogEntry('warn', message, data);
        console.warn(`[WARN] ${message}`, data);
        this.writeToFile(logEntry);
    }
    error(message, error) {
        const stack = error instanceof Error ? error.stack : undefined;
        const data = error instanceof Error ? { name: error.name, message: error.message } : error;
        const logEntry = this.formatLogEntry('error', message, data, stack);
        console.error(`[ERROR] ${message}`, error);
        this.writeToFile(logEntry);
    }
    // Get recent log entries
    getRecentLogs(count = 100) {
        try {
            if (!fs.existsSync(this.logFile)) {
                return [];
            }
            const content = fs.readFileSync(this.logFile, 'utf-8');
            const lines = content.trim().split('\n').filter(line => line.trim());
            return lines
                .slice(-count)
                .map(line => {
                try {
                    return JSON.parse(line);
                }
                catch {
                    return {
                        timestamp: new Date().toISOString(),
                        level: 'info',
                        message: line,
                    };
                }
            });
        }
        catch (error) {
            console.error('Failed to read log file:', error);
            return [];
        }
    }
    // Clear all logs
    clearLogs() {
        try {
            if (fs.existsSync(this.logFile)) {
                fs.unlinkSync(this.logFile);
            }
            // Remove rotated logs
            for (let i = 1; i <= this.maxLogFiles; i++) {
                const logFile = path.join(this.logDir, `app.log.${i}`);
                if (fs.existsSync(logFile)) {
                    fs.unlinkSync(logFile);
                }
            }
        }
        catch (error) {
            console.error('Failed to clear logs:', error);
        }
    }
    // Get log file paths
    getLogFiles() {
        try {
            const files = fs.readdirSync(this.logDir)
                .filter(file => file.startsWith('app.log'))
                .map(file => path.join(this.logDir, file))
                .sort((a, b) => {
                const aStats = fs.statSync(a);
                const bStats = fs.statSync(b);
                return bStats.mtime.getTime() - aStats.mtime.getTime();
            });
            return files;
        }
        catch (error) {
            console.error('Failed to get log files:', error);
            return [];
        }
    }
    // Get log directory size
    getLogDirectorySize() {
        try {
            let totalSize = 0;
            const files = fs.readdirSync(this.logDir);
            for (const file of files) {
                const filePath = path.join(this.logDir, file);
                const stats = fs.statSync(filePath);
                totalSize += stats.size;
            }
            return totalSize;
        }
        catch (error) {
            console.error('Failed to get log directory size:', error);
            return 0;
        }
    }
}
// Create singleton instance
exports.logger = new Logger();
// Error handling utilities
class AppError extends Error {
    constructor(message, code = 'UNKNOWN_ERROR', statusCode = 500, isOperational = true) {
        super(message);
        this.name = 'AppError';
        this.code = code;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
// Common error types
class ValidationError extends AppError {
    constructor(message, field) {
        super(message, 'VALIDATION_ERROR', 400);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends AppError {
    constructor(resource) {
        super(`${resource} not found`, 'NOT_FOUND', 404);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends AppError {
    constructor(message = 'Unauthorized') {
        super(message, 'UNAUTHORIZED', 401);
        this.name = 'UnauthorizedError';
    }
}
exports.UnauthorizedError = UnauthorizedError;
class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 'RATE_LIMIT_EXCEEDED', 429);
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
class NetworkError extends AppError {
    constructor(message = 'Network error') {
        super(message, 'NETWORK_ERROR', 503);
        this.name = 'NetworkError';
    }
}
exports.NetworkError = NetworkError;
// Error handler function
const handleError = (error, context) => {
    const contextMessage = context ? `[${context}] ` : '';
    if (error instanceof AppError) {
        if (error.isOperational) {
            exports.logger.warn(`${contextMessage}Operational error: ${error.message}`, {
                code: error.code,
                statusCode: error.statusCode,
                stack: error.stack,
            });
        }
        else {
            exports.logger.error(`${contextMessage}Programming error: ${error.message}`, error);
        }
    }
    else {
        exports.logger.error(`${contextMessage}Unexpected error: ${error.message}`, error);
    }
};
exports.handleError = handleError;
// Async error wrapper
const asyncErrorHandler = (fn) => {
    return async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            (0, exports.handleError)(error, fn.name);
            throw error;
        }
    };
};
exports.asyncErrorHandler = asyncErrorHandler;
// Performance monitoring
class PerformanceMonitor {
    static start(label) {
        this.timers.set(label, Date.now());
    }
    static end(label) {
        const startTime = this.timers.get(label);
        if (!startTime) {
            exports.logger.warn(`Performance timer '${label}' was not started`);
            return 0;
        }
        const duration = Date.now() - startTime;
        this.timers.delete(label);
        exports.logger.debug(`Performance: ${label} took ${duration}ms`);
        return duration;
    }
    static measure(label, fn) {
        this.start(label);
        try {
            const result = fn();
            this.end(label);
            return result;
        }
        catch (error) {
            this.end(label);
            throw error;
        }
    }
    static async measureAsync(label, fn) {
        this.start(label);
        try {
            const result = await fn();
            this.end(label);
            return result;
        }
        catch (error) {
            this.end(label);
            throw error;
        }
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
PerformanceMonitor.timers = new Map();
//# sourceMappingURL=logger.js.map