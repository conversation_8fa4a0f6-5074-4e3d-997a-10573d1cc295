import type { ToolSchema } from '@shared/types';
export declare const category: "file_system";
export declare const schema: ToolSchema;
export declare function execute(args: {
    path: string;
    content: string;
    encoding?: string;
    createDirectories?: boolean;
    overwrite?: boolean;
}): Promise<{
    path: string;
    absolutePath: string;
    bytesWritten: number;
    created: boolean;
    encoding: string;
    timestamp: string;
}>;
export declare function appendToFile(args: {
    path: string;
    content: string;
    encoding?: string;
    createIfNotExists?: boolean;
}): Promise<{
    path: string;
    absolutePath: string;
    bytesAppended: number;
    fileSize: number;
    timestamp: string;
}>;
//# sourceMappingURL=write_file.d.ts.map