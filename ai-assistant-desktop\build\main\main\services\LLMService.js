"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMService = void 0;
const OpenAIProvider_1 = require("./providers/OpenAIProvider");
const AnthropicProvider_1 = require("./providers/AnthropicProvider");
const DeepSeekProvider_1 = require("./providers/DeepSeekProvider");
class LLMService {
    constructor() {
        this.providers = new Map();
        this.initializeProviders();
    }
    initializeProviders() {
        const openai = new OpenAIProvider_1.OpenAIProvider();
        const anthropic = new AnthropicProvider_1.AnthropicProvider();
        const deepseek = new DeepSeekProvider_1.DeepSeekProvider();
        this.providers.set('openai', openai);
        this.providers.set('anthropic', anthropic);
        this.providers.set('deepseek', deepseek);
    }
    getProvider(providerName) {
        const provider = this.providers.get(providerName);
        if (!provider) {
            throw new Error(`Unsupported LLM provider: ${providerName}`);
        }
        return provider;
    }
    async chat(request, config) {
        const provider = this.getProvider(config.provider);
        if (!provider.validateConfig(config)) {
            throw new Error(`Invalid configuration for provider: ${config.provider}`);
        }
        return await provider.chat(request.messages, config, request.systemPrompt);
    }
    async streamChat(request, config) {
        const provider = this.getProvider(config.provider);
        if (!provider.validateConfig(config)) {
            throw new Error(`Invalid configuration for provider: ${config.provider}`);
        }
        return provider.streamChat(request.messages, config, request.systemPrompt);
    }
    countTokens(text, config) {
        const provider = this.getProvider(config.provider);
        return provider.countTokens(text, config.model);
    }
    getAvailableProviders() {
        return Array.from(this.providers.keys());
    }
    getProviderModels(providerName) {
        const provider = this.getProvider(providerName);
        return provider.supportedModels;
    }
    validateProviderConfig(config) {
        try {
            const provider = this.getProvider(config.provider);
            return provider.validateConfig(config);
        }
        catch {
            return false;
        }
    }
    // Context window optimization
    optimizeContextWindow(messages, config, maxTokens) {
        const provider = this.getProvider(config.provider);
        const contextLimit = maxTokens || config.maxTokens || 4000;
        const reservedTokens = 1000; // Reserve tokens for response
        const availableTokens = contextLimit - reservedTokens;
        if (messages.length === 0) {
            return messages;
        }
        // Always keep the last user message
        const lastMessage = messages[messages.length - 1];
        const otherMessages = messages.slice(0, -1);
        let currentTokens = provider.countTokens(lastMessage.content, config.model);
        const optimizedMessages = [lastMessage];
        // Add messages from most recent to oldest while staying under token limit
        for (let i = otherMessages.length - 1; i >= 0; i--) {
            const message = otherMessages[i];
            const messageTokens = provider.countTokens(message.content, config.model);
            if (currentTokens + messageTokens <= availableTokens) {
                currentTokens += messageTokens;
                optimizedMessages.unshift(message);
            }
            else {
                // If we can't fit the entire message, try to summarize older messages
                break;
            }
        }
        return optimizedMessages;
    }
    // Smart context summarization for when context window is exceeded
    async summarizeContext(messages, config, summaryTarget = 500) {
        if (messages.length <= 2) {
            return messages;
        }
        // Keep the first system message (if any) and last user message
        const systemMessages = messages.filter(m => m.role === 'system');
        const lastMessage = messages[messages.length - 1];
        const middleMessages = messages.slice(systemMessages.length, messages.length - 1);
        if (middleMessages.length === 0) {
            return messages;
        }
        // Create a summary of the middle conversation
        const conversationText = middleMessages
            .map(m => `${m.role}: ${m.content}`)
            .join('\n\n');
        const summaryPrompt = [
            {
                id: 'summary-request',
                role: 'user',
                content: `Please provide a concise summary of the following conversation history. Focus on key points, decisions made, and important context that would be relevant for continuing the conversation. Target length: ${summaryTarget} tokens.\n\nConversation:\n${conversationText}`,
                timestamp: new Date(),
            }
        ];
        try {
            const summaryResponse = await this.chat({ messages: summaryPrompt }, config);
            const summaryMessage = {
                id: 'context-summary',
                role: 'assistant',
                content: `[Conversation Summary]: ${summaryResponse.content}`,
                timestamp: new Date(),
            };
            return [
                ...systemMessages,
                summaryMessage,
                lastMessage,
            ];
        }
        catch (error) {
            console.error('Failed to summarize context:', error);
            // Fallback: just return recent messages
            return [
                ...systemMessages,
                ...messages.slice(-3),
            ];
        }
    }
}
exports.LLMService = LLMService;
//# sourceMappingURL=LLMService.js.map