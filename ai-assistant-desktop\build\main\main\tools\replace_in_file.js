"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const crypto_1 = require("crypto");
const constants_1 = require("@shared/constants");
exports.category = constants_1.TOOL_CATEGORIES.FILE_SYSTEM;
exports.schema = {
    name: 'replace_in_file',
    description: 'Replace exact text blocks in a file with new content. This is a safer alternative to generic file editing as it requires exact matching of the text to be replaced.',
    category: constants_1.TOOL_CATEGORIES.FILE_SYSTEM,
    riskLevel: 'medium',
    parameters: {
        type: 'object',
        properties: {
            filePath: {
                type: 'string',
                description: 'Path to the file to modify',
                required: true,
            },
            oldText: {
                type: 'string',
                description: 'Exact text block to replace (must match exactly including whitespace)',
                required: true,
            },
            newText: {
                type: 'string',
                description: 'New text to replace the old text with',
                required: true,
            },
            createBackup: {
                type: 'boolean',
                description: 'Whether to create a backup file before making changes (default: true)',
                required: false,
                default: true,
            },
        },
        required: ['filePath', 'oldText', 'newText'],
    },
};
async function execute(args) {
    const { filePath, oldText, newText, createBackup = true } = args;
    try {
        // Validate file path
        const resolvedPath = path.resolve(filePath);
        // Check if file exists
        try {
            await fs.access(resolvedPath);
        }
        catch {
            return {
                success: false,
                message: `File not found: ${filePath}`,
                changes: {
                    filePath: resolvedPath,
                    oldTextHash: '',
                    newTextHash: '',
                    linesChanged: 0,
                    diff: '',
                },
                error: 'File not found',
            };
        }
        // Read current file content
        const originalContent = await fs.readFile(resolvedPath, 'utf-8');
        // Check if old text exists in the file
        if (!originalContent.includes(oldText)) {
            return {
                success: false,
                message: `Text to replace not found in file: ${filePath}`,
                changes: {
                    filePath: resolvedPath,
                    oldTextHash: (0, crypto_1.createHash)('md5').update(oldText).digest('hex'),
                    newTextHash: (0, crypto_1.createHash)('md5').update(newText).digest('hex'),
                    linesChanged: 0,
                    diff: 'No matching text found',
                },
                error: 'Text not found',
            };
        }
        // Count occurrences to warn about multiple matches
        const occurrences = (originalContent.match(new RegExp(escapeRegExp(oldText), 'g')) || []).length;
        if (occurrences > 1) {
            return {
                success: false,
                message: `Multiple occurrences (${occurrences}) of the text found. Please be more specific to avoid unintended replacements.`,
                changes: {
                    filePath: resolvedPath,
                    oldTextHash: (0, crypto_1.createHash)('md5').update(oldText).digest('hex'),
                    newTextHash: (0, crypto_1.createHash)('md5').update(newText).digest('hex'),
                    linesChanged: 0,
                    diff: `Found ${occurrences} occurrences`,
                },
                error: 'Multiple matches found',
            };
        }
        // Create backup if requested
        let backupPath;
        if (createBackup) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            backupPath = `${resolvedPath}.backup.${timestamp}`;
            await fs.copyFile(resolvedPath, backupPath);
        }
        // Perform replacement
        const newContent = originalContent.replace(oldText, newText);
        // Calculate changes
        const oldLines = oldText.split('\n').length;
        const newLines = newText.split('\n').length;
        const linesChanged = Math.abs(newLines - oldLines);
        // Generate diff
        const diff = generateDiff(oldText, newText);
        // Write new content
        await fs.writeFile(resolvedPath, newContent, 'utf-8');
        return {
            success: true,
            message: `Successfully replaced text in ${filePath}${backupPath ? ` (backup created: ${path.basename(backupPath)})` : ''}`,
            changes: {
                filePath: resolvedPath,
                backupPath,
                oldTextHash: (0, crypto_1.createHash)('md5').update(oldText).digest('hex'),
                newTextHash: (0, crypto_1.createHash)('md5').update(newText).digest('hex'),
                linesChanged,
                diff,
            },
        };
    }
    catch (error) {
        return {
            success: false,
            message: `Failed to replace text in file: ${error.message}`,
            changes: {
                filePath: path.resolve(filePath),
                oldTextHash: (0, crypto_1.createHash)('md5').update(oldText).digest('hex'),
                newTextHash: (0, crypto_1.createHash)('md5').update(newText).digest('hex'),
                linesChanged: 0,
                diff: '',
            },
            error: error.message,
        };
    }
}
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
function generateDiff(oldText, newText) {
    const oldLines = oldText.split('\n');
    const newLines = newText.split('\n');
    let diff = '';
    const maxLines = Math.max(oldLines.length, newLines.length);
    for (let i = 0; i < maxLines; i++) {
        const oldLine = oldLines[i];
        const newLine = newLines[i];
        if (oldLine !== undefined && newLine !== undefined) {
            if (oldLine !== newLine) {
                diff += `- ${oldLine}\n+ ${newLine}\n`;
            }
            else {
                diff += `  ${oldLine}\n`;
            }
        }
        else if (oldLine !== undefined) {
            diff += `- ${oldLine}\n`;
        }
        else if (newLine !== undefined) {
            diff += `+ ${newLine}\n`;
        }
    }
    return diff;
}
//# sourceMappingURL=replace_in_file.js.map