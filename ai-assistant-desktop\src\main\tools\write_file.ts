import * as fs from 'fs/promises';
import * as path from 'path';
import type { ToolSchema } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

export const category = TOOL_CATEGORIES.FILE_SYSTEM;

export const schema: ToolSchema = {
  name: 'write_file',
  description: 'Write content to a file on the filesystem. Creates directories if they do not exist. Supports text and binary content.',
  category: TOOL_CATEGORIES.FILE_SYSTEM,
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The absolute or relative path where to write the file',
      },
      content: {
        type: 'string',
        description: 'The content to write to the file',
      },
      encoding: {
        type: 'string',
        description: 'Text encoding to use (default: utf8). Use "base64" for binary content.',
        default: 'utf8',
      },
      createDirectories: {
        type: 'boolean',
        description: 'Create parent directories if they do not exist (default: true)',
        default: true,
      },
      overwrite: {
        type: 'boolean',
        description: 'Overwrite file if it already exists (default: true)',
        default: true,
      },
    },
    required: ['path', 'content'],
  },
  examples: [
    {
      description: 'Write a text file',
      arguments: { path: './output.txt', content: 'Hello, World!' },
      expectedOutput: 'File written successfully',
    },
  ],
  riskLevel: 'medium',
  requiresConfirmation: true,
};

export async function execute(args: {
  path: string;
  content: string;
  encoding?: string;
  createDirectories?: boolean;
  overwrite?: boolean;
}): Promise<{
  path: string;
  absolutePath: string;
  bytesWritten: number;
  created: boolean;
  encoding: string;
  timestamp: string;
}> {
  const { 
    path: filePath, 
    content, 
    encoding = 'utf8', 
    createDirectories = true, 
    overwrite = true 
  } = args;
  
  try {
    // Resolve to absolute path
    const absolutePath = path.resolve(filePath);
    
    // Check if file exists
    let existed = false;
    try {
      await fs.access(absolutePath);
      existed = true;
      
      if (!overwrite) {
        throw new Error(`File already exists and overwrite is disabled: ${absolutePath}`);
      }
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        throw error;
      }
    }
    
    // Create parent directories if needed
    if (createDirectories) {
      const dirPath = path.dirname(absolutePath);
      await fs.mkdir(dirPath, { recursive: true });
    }
    
    // Write content
    let bytesWritten: number;
    if (encoding === 'base64') {
      // Handle binary content
      const buffer = Buffer.from(content, 'base64');
      await fs.writeFile(absolutePath, buffer);
      bytesWritten = buffer.length;
    } else {
      // Handle text content
      await fs.writeFile(absolutePath, content, { encoding: encoding as BufferEncoding });
      bytesWritten = Buffer.byteLength(content, encoding as BufferEncoding);
    }
    
    return {
      path: filePath,
      absolutePath,
      bytesWritten,
      created: !existed,
      encoding,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    if (error.code === 'EACCES') {
      throw new Error(`Permission denied: ${filePath}`);
    } else if (error.code === 'ENOTDIR') {
      throw new Error(`Parent path is not a directory: ${filePath}`);
    } else if (error.code === 'ENOSPC') {
      throw new Error(`No space left on device: ${filePath}`);
    } else {
      throw new Error(`Error writing file: ${error.message}`);
    }
  }
}

// Helper function to append to file
export async function appendToFile(args: {
  path: string;
  content: string;
  encoding?: string;
  createIfNotExists?: boolean;
}): Promise<{
  path: string;
  absolutePath: string;
  bytesAppended: number;
  fileSize: number;
  timestamp: string;
}> {
  const { 
    path: filePath, 
    content, 
    encoding = 'utf8', 
    createIfNotExists = true 
  } = args;
  
  try {
    const absolutePath = path.resolve(filePath);
    
    // Check if file exists
    let existed = true;
    try {
      await fs.access(absolutePath);
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        existed = false;
        if (!createIfNotExists) {
          throw new Error(`File does not exist: ${absolutePath}`);
        }
      } else {
        throw error;
      }
    }
    
    // Append content
    let bytesAppended: number;
    if (encoding === 'base64') {
      const buffer = Buffer.from(content, 'base64');
      await fs.appendFile(absolutePath, buffer);
      bytesAppended = buffer.length;
    } else {
      await fs.appendFile(absolutePath, content, { encoding: encoding as BufferEncoding });
      bytesAppended = Buffer.byteLength(content, encoding as BufferEncoding);
    }
    
    // Get final file size
    const stats = await fs.stat(absolutePath);
    
    return {
      path: filePath,
      absolutePath,
      bytesAppended,
      fileSize: stats.size,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    throw new Error(`Error appending to file: ${error.message}`);
  }
}