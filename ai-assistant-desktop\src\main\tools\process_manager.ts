import { spawn, exec, ChildProcess } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';
import type { ToolSchema } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

const execAsync = promisify(exec);

// Global process registry
const runningProcesses = new Map<string, {
  process: ChildProcess;
  command: string;
  startTime: Date;
  pid?: number;
}>();

export const category = TOOL_CATEGORIES.SYSTEM;

export const schema: ToolSchema = {
  name: 'process_manager',
  description: 'Manage system processes: spawn background processes, list running processes, and kill processes.',
  category: TOOL_CATEGORIES.SYSTEM,
  riskLevel: 'high',
  parameters: {
    type: 'object',
    properties: {
      action: {
        type: 'string',
        description: 'Action to perform: "spawn", "list", "kill", "status"',
        required: true,
      },
      command: {
        type: 'string',
        description: 'Command to spawn (required for "spawn" action)',
        required: false,
      },
      args: {
        type: 'array',
        description: 'Command arguments (for "spawn" action)',
        required: false,
      },
      cwd: {
        type: 'string',
        description: 'Working directory for spawned process',
        required: false,
      },
      env: {
        type: 'object',
        description: 'Environment variables for spawned process',
        required: false,
      },
      processId: {
        type: 'string',
        description: 'Process ID for "kill" or "status" actions (internal ID or system PID)',
        required: false,
      },
      signal: {
        type: 'string',
        description: 'Signal to send when killing process (default: SIGTERM)',
        required: false,
        default: 'SIGTERM',
      },
      detached: {
        type: 'boolean',
        description: 'Whether to spawn process in detached mode (default: true)',
        required: false,
        default: true,
      },
    },
    required: ['action'],
  },
};

export async function execute(args: {
  action: 'spawn' | 'list' | 'kill' | 'status';
  command?: string;
  args?: string[];
  cwd?: string;
  env?: Record<string, string>;
  processId?: string;
  signal?: string;
  detached?: boolean;
}): Promise<{
  success: boolean;
  message: string;
  data: any;
  error?: string;
}> {
  const { action, command, args: cmdArgs = [], cwd, env, processId, signal = 'SIGTERM', detached = true } = args;

  try {
    switch (action) {
      case 'spawn':
        return await spawnProcess({ command: command!, args: cmdArgs, cwd, env, detached });
      
      case 'list':
        return await listProcesses();
      
      case 'kill':
        return await killProcess(processId!, signal);
      
      case 'status':
        return await getProcessStatus(processId!);
      
      default:
        return {
          success: false,
          message: `Unknown action: ${action}`,
          data: null,
          error: 'Invalid action',
        };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Process manager error: ${error.message}`,
      data: null,
      error: error.message,
    };
  }
}

async function spawnProcess(options: {
  command: string;
  args: string[];
  cwd?: string;
  env?: Record<string, string>;
  detached: boolean;
}): Promise<{ success: boolean; message: string; data: any; error?: string }> {
  const { command, args, cwd, env, detached } = options;

  try {
    const processId = generateProcessId();
    const workingDir = cwd ? path.resolve(cwd) : process.cwd();
    
    const spawnOptions: any = {
      cwd: workingDir,
      env: { ...process.env, ...env },
      detached,
      stdio: detached ? 'ignore' : 'pipe',
    };

    const childProcess = spawn(command, args, spawnOptions);

    if (!childProcess.pid) {
      return {
        success: false,
        message: `Failed to spawn process: ${command}`,
        data: null,
        error: 'Process spawn failed',
      };
    }

    // Register the process
    runningProcesses.set(processId, {
      process: childProcess,
      command: `${command} ${args.join(' ')}`,
      startTime: new Date(),
      pid: childProcess.pid,
    });

    // Handle process events
    childProcess.on('error', (error) => {
      console.error(`Process ${processId} error:`, error);
      runningProcesses.delete(processId);
    });

    childProcess.on('exit', (code, signal) => {
      console.log(`Process ${processId} exited with code ${code}, signal ${signal}`);
      runningProcesses.delete(processId);
    });

    if (detached) {
      childProcess.unref();
    }

    return {
      success: true,
      message: `Process spawned successfully: ${command}`,
      data: {
        processId,
        pid: childProcess.pid,
        command: `${command} ${args.join(' ')}`,
        startTime: new Date().toISOString(),
        detached,
      },
    };

  } catch (error: any) {
    return {
      success: false,
      message: `Failed to spawn process: ${error.message}`,
      data: null,
      error: error.message,
    };
  }
}

async function listProcesses(): Promise<{ success: boolean; message: string; data: any; error?: string }> {
  try {
    // Get our managed processes
    const managedProcesses = Array.from(runningProcesses.entries()).map(([id, info]) => ({
      id,
      pid: info.pid,
      command: info.command,
      startTime: info.startTime.toISOString(),
      uptime: Date.now() - info.startTime.getTime(),
      managed: true,
    }));

    // Get system processes
    const systemProcesses = await getSystemProcesses();

    return {
      success: true,
      message: `Found ${managedProcesses.length} managed processes and ${systemProcesses.length} system processes`,
      data: {
        managed: managedProcesses,
        system: systemProcesses.slice(0, 50), // Limit system processes to avoid overwhelming output
        total: {
          managed: managedProcesses.length,
          system: systemProcesses.length,
        },
      },
    };

  } catch (error: any) {
    return {
      success: false,
      message: `Failed to list processes: ${error.message}`,
      data: null,
      error: error.message,
    };
  }
}

async function killProcess(processId: string, signal: string): Promise<{ success: boolean; message: string; data: any; error?: string }> {
  try {
    // Check if it's a managed process first
    const managedProcess = runningProcesses.get(processId);
    if (managedProcess) {
      try {
        managedProcess.process.kill(signal as NodeJS.Signals);
        runningProcesses.delete(processId);
        
        return {
          success: true,
          message: `Managed process ${processId} killed with signal ${signal}`,
          data: {
            processId,
            pid: managedProcess.pid,
            command: managedProcess.command,
            signal,
          },
        };
      } catch (error: any) {
        return {
          success: false,
          message: `Failed to kill managed process: ${error.message}`,
          data: null,
          error: error.message,
        };
      }
    }

    // Try to kill by system PID
    const pid = parseInt(processId);
    if (!isNaN(pid)) {
      try {
        process.kill(pid, signal as NodeJS.Signals);
        
        return {
          success: true,
          message: `System process ${pid} killed with signal ${signal}`,
          data: {
            pid,
            signal,
          },
        };
      } catch (error: any) {
        return {
          success: false,
          message: `Failed to kill system process: ${error.message}`,
          data: null,
          error: error.message,
        };
      }
    }

    return {
      success: false,
      message: `Process not found: ${processId}`,
      data: null,
      error: 'Process not found',
    };

  } catch (error: any) {
    return {
      success: false,
      message: `Failed to kill process: ${error.message}`,
      data: null,
      error: error.message,
    };
  }
}

async function getProcessStatus(processId: string): Promise<{ success: boolean; message: string; data: any; error?: string }> {
  try {
    const managedProcess = runningProcesses.get(processId);
    if (managedProcess) {
      const uptime = Date.now() - managedProcess.startTime.getTime();
      
      return {
        success: true,
        message: `Process ${processId} is running`,
        data: {
          processId,
          pid: managedProcess.pid,
          command: managedProcess.command,
          startTime: managedProcess.startTime.toISOString(),
          uptime,
          status: 'running',
          managed: true,
        },
      };
    }

    return {
      success: false,
      message: `Process not found: ${processId}`,
      data: null,
      error: 'Process not found',
    };

  } catch (error: any) {
    return {
      success: false,
      message: `Failed to get process status: ${error.message}`,
      data: null,
      error: error.message,
    };
  }
}

async function getSystemProcesses(): Promise<Array<{
  pid: number;
  name: string;
  cpu?: number;
  memory?: number;
}>> {
  try {
    let command: string;
    
    if (process.platform === 'win32') {
      command = 'wmic process get ProcessId,Name,PageFileUsage,WorkingSetSize /format:csv';
    } else if (process.platform === 'darwin') {
      command = 'ps -axo pid,comm,%cpu,%mem';
    } else {
      command = 'ps -axo pid,comm,%cpu,%mem';
    }

    const result = await execAsync(command);
    const lines = result.stdout.trim().split('\n');
    const processes: Array<{ pid: number; name: string; cpu?: number; memory?: number }> = [];

    for (const line of lines.slice(1)) { // Skip header
      if (!line.trim()) continue;

      if (process.platform === 'win32') {
        const parts = line.split(',').map(p => p.trim());
        if (parts.length >= 4 && parts[3]) {
          processes.push({
            pid: parseInt(parts[3]) || 0,
            name: parts[1] || 'Unknown',
          });
        }
      } else {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 4) {
          processes.push({
            pid: parseInt(parts[0]) || 0,
            name: parts[1] || 'Unknown',
            cpu: parseFloat(parts[2]) || 0,
            memory: parseFloat(parts[3]) || 0,
          });
        }
      }
    }

    return processes.filter(p => p.pid > 0);

  } catch (error) {
    console.error('Failed to get system processes:', error);
    return [];
  }
}

function generateProcessId(): string {
  return `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
