"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
exports.readFileChunked = readFileChunked;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const mime_types_1 = require("mime-types");
const constants_1 = require("@shared/constants");
exports.category = constants_1.TOOL_CATEGORIES.FILE_SYSTEM;
exports.schema = {
    name: 'read_file',
    description: 'Read the contents of a file from the filesystem. Supports text files, with automatic encoding detection and binary file handling.',
    category: constants_1.TOOL_CATEGORIES.FILE_SYSTEM,
    parameters: {
        type: 'object',
        properties: {
            path: {
                type: 'string',
                description: 'The absolute or relative path to the file to read',
            },
            encoding: {
                type: 'string',
                description: 'Text encoding to use (default: utf8). Use "binary" for binary files.',
                default: 'utf8',
            },
            maxSize: {
                type: 'number',
                description: 'Maximum file size to read in bytes (default: 10MB)',
                default: 10 * 1024 * 1024,
            },
        },
        required: ['path'],
    },
    examples: [
        {
            description: 'Read a text file',
            arguments: { path: './README.md' },
            expectedOutput: 'File contents as text',
        },
        {
            description: 'Read a binary file as base64',
            arguments: { path: './image.png', encoding: 'binary' },
            expectedOutput: 'Base64 encoded binary data',
        },
    ],
    riskLevel: 'low',
};
async function execute(args) {
    const { path: filePath, encoding = 'utf8', maxSize = 10 * 1024 * 1024 } = args;
    try {
        // Resolve to absolute path
        const absolutePath = path.resolve(filePath);
        // Check if file exists and get stats
        const stats = await fs.stat(absolutePath);
        if (!stats.isFile()) {
            throw new Error(`Path is not a file: ${absolutePath}`);
        }
        if (stats.size > maxSize) {
            throw new Error(`File too large: ${stats.size} bytes (max: ${maxSize} bytes)`);
        }
        // Determine MIME type
        const mimeType = (0, mime_types_1.lookup)(absolutePath) || 'application/octet-stream';
        // Read file content
        let content;
        if (encoding === 'binary' || mimeType.startsWith('image/') || mimeType.startsWith('application/')) {
            // For binary files, return base64 encoded content
            const buffer = await fs.readFile(absolutePath);
            content = buffer.toString('base64');
        }
        else {
            // For text files, use specified encoding
            content = await fs.readFile(absolutePath, { encoding: encoding });
        }
        return {
            content,
            size: stats.size,
            mimeType,
            encoding: encoding === 'binary' ? 'base64' : encoding,
            lastModified: stats.mtime.toISOString(),
            absolutePath,
        };
    }
    catch (error) {
        if (error.code === 'ENOENT') {
            throw new Error(`File not found: ${filePath}`);
        }
        else if (error.code === 'EACCES') {
            throw new Error(`Permission denied: ${filePath}`);
        }
        else if (error.code === 'EISDIR') {
            throw new Error(`Path is a directory: ${filePath}`);
        }
        else {
            throw new Error(`Error reading file: ${error.message}`);
        }
    }
}
// Helper function to read file in chunks for large files
async function readFileChunked(args) {
    const { path: filePath, chunkSize = 1024 * 1024, startOffset = 0, endOffset } = args;
    try {
        const absolutePath = path.resolve(filePath);
        const stats = await fs.stat(absolutePath);
        if (!stats.isFile()) {
            throw new Error(`Path is not a file: ${absolutePath}`);
        }
        const totalSize = stats.size;
        const actualEndOffset = endOffset !== undefined ? Math.min(endOffset, totalSize) : Math.min(startOffset + chunkSize, totalSize);
        // Open file and read chunk
        const fileHandle = await fs.open(absolutePath, 'r');
        const buffer = Buffer.alloc(actualEndOffset - startOffset);
        try {
            await fileHandle.read(buffer, 0, actualEndOffset - startOffset, startOffset);
            const content = buffer.toString('utf8');
            return {
                content,
                totalSize,
                chunkStart: startOffset,
                chunkEnd: actualEndOffset,
                hasMore: actualEndOffset < totalSize,
            };
        }
        finally {
            await fileHandle.close();
        }
    }
    catch (error) {
        throw new Error(`Error reading file chunk: ${error.message}`);
    }
}
//# sourceMappingURL=read_file.js.map