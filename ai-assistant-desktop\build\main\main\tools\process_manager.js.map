{"version": 3, "file": "process_manager.js", "sourceRoot": "", "sources": ["../../../../src/main/tools/process_manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,0BA+CC;AAxHD,iDAA0D;AAC1D,+BAAiC;AACjC,2CAA6B;AAE7B,iDAAoD;AAEpD,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AAElC,0BAA0B;AAC1B,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAK5B,CAAC;AAEQ,QAAA,QAAQ,GAAG,2BAAe,CAAC,MAAM,CAAC;AAElC,QAAA,MAAM,GAAe;IAChC,IAAI,EAAE,iBAAiB;IACvB,WAAW,EAAE,kGAAkG;IAC/G,QAAQ,EAAE,2BAAe,CAAC,MAAM;IAChC,SAAS,EAAE,MAAM;IACjB,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,sDAAsD;gBACnE,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,gDAAgD;gBAC7D,QAAQ,EAAE,KAAK;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,wCAAwC;gBACrD,QAAQ,EAAE,KAAK;aAChB;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,KAAK;aAChB;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,2CAA2C;gBACxD,QAAQ,EAAE,KAAK;aAChB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,uEAAuE;gBACpF,QAAQ,EAAE,KAAK;aAChB;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,wDAAwD;gBACrE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,SAAS;aACnB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,2DAA2D;gBACxE,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;aACd;SACF;QACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;KACrB;CACF,CAAC;AAEK,KAAK,UAAU,OAAO,CAAC,IAS7B;IAMC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,SAAS,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;IAE/G,IAAI,CAAC;QACH,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO;gBACV,OAAO,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,OAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEtF,KAAK,MAAM;gBACT,OAAO,MAAM,aAAa,EAAE,CAAC;YAE/B,KAAK,MAAM;gBACT,OAAO,MAAM,WAAW,CAAC,SAAU,EAAE,MAAM,CAAC,CAAC;YAE/C,KAAK,QAAQ;gBACX,OAAO,MAAM,gBAAgB,CAAC,SAAU,CAAC,CAAC;YAE5C;gBACE,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,mBAAmB,MAAM,EAAE;oBACpC,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,gBAAgB;iBACxB,CAAC;QACN,CAAC;IACH,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0BAA0B,KAAK,CAAC,OAAO,EAAE;YAClD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,OAM3B;IACC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAEtD,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,iBAAiB,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QAE3D,MAAM,YAAY,GAAQ;YACxB,GAAG,EAAE,UAAU;YACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE;YAC/B,QAAQ;YACR,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;SACpC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAA,qBAAK,EAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;QAExD,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YACtB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,4BAA4B,OAAO,EAAE;gBAC9C,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,sBAAsB;aAC9B,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE;YAC9B,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACvC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,GAAG,EAAE,YAAY,CAAC,GAAG;SACtB,CAAC,CAAC;QAEH,wBAAwB;QACxB,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,WAAW,SAAS,SAAS,EAAE,KAAK,CAAC,CAAC;YACpD,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACvC,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,qBAAqB,IAAI,YAAY,MAAM,EAAE,CAAC,CAAC;YAC/E,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,YAAY,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,iCAAiC,OAAO,EAAE;YACnD,IAAI,EAAE;gBACJ,SAAS;gBACT,GAAG,EAAE,YAAY,CAAC,GAAG;gBACrB,OAAO,EAAE,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACvC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,QAAQ;aACT;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,4BAA4B,KAAK,CAAC,OAAO,EAAE;YACpD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,aAAa;IAC1B,IAAI,CAAC;QACH,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YACnF,EAAE;YACF,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC7C,OAAO,EAAE,IAAI;SACd,CAAC,CAAC,CAAC;QAEJ,uBAAuB;QACvB,MAAM,eAAe,GAAG,MAAM,kBAAkB,EAAE,CAAC;QAEnD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,SAAS,gBAAgB,CAAC,MAAM,0BAA0B,eAAe,CAAC,MAAM,mBAAmB;YAC5G,IAAI,EAAE;gBACJ,OAAO,EAAE,gBAAgB;gBACzB,MAAM,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,sDAAsD;gBAC5F,KAAK,EAAE;oBACL,OAAO,EAAE,gBAAgB,CAAC,MAAM;oBAChC,MAAM,EAAE,eAAe,CAAC,MAAM;iBAC/B;aACF;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;YACrD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,SAAiB,EAAE,MAAc;IAC1D,IAAI,CAAC;QACH,wCAAwC;QACxC,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,MAAwB,CAAC,CAAC;gBACtD,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAEnC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,mBAAmB,SAAS,uBAAuB,MAAM,EAAE;oBACpE,IAAI,EAAE;wBACJ,SAAS;wBACT,GAAG,EAAE,cAAc,CAAC,GAAG;wBACvB,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,MAAM;qBACP;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,mCAAmC,KAAK,CAAC,OAAO,EAAE;oBAC3D,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,MAAwB,CAAC,CAAC;gBAE5C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,kBAAkB,GAAG,uBAAuB,MAAM,EAAE;oBAC7D,IAAI,EAAE;wBACJ,GAAG;wBACH,MAAM;qBACP;iBACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,kCAAkC,KAAK,CAAC,OAAO,EAAE;oBAC1D,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,sBAAsB,SAAS,EAAE;YAC1C,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,mBAAmB;SAC3B,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2BAA2B,KAAK,CAAC,OAAO,EAAE;YACnD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,SAAiB;IAC/C,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAE/D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,WAAW,SAAS,aAAa;gBAC1C,IAAI,EAAE;oBACJ,SAAS;oBACT,GAAG,EAAE,cAAc,CAAC,GAAG;oBACvB,OAAO,EAAE,cAAc,CAAC,OAAO;oBAC/B,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE;oBACjD,MAAM;oBACN,MAAM,EAAE,SAAS;oBACjB,OAAO,EAAE,IAAI;iBACd;aACF,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,sBAAsB,SAAS,EAAE;YAC1C,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,mBAAmB;SAC3B,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,iCAAiC,KAAK,CAAC,OAAO,EAAE;YACzD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,kBAAkB;IAM/B,IAAI,CAAC;QACH,IAAI,OAAe,CAAC;QAEpB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,GAAG,0EAA0E,CAAC;QACvF,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,GAAG,4BAA4B,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,4BAA4B,CAAC;QACzC,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,SAAS,GAAwE,EAAE,CAAC;QAE1F,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,cAAc;YACjD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAAE,SAAS;YAE3B,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBACjD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBAClC,SAAS,CAAC,IAAI,CAAC;wBACb,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC5B,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS;qBAC5B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACvC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACtB,SAAS,CAAC,IAAI,CAAC;wBACb,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC5B,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS;wBAC3B,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC9B,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;qBAClC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAE1C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB;IACxB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACzE,CAAC"}