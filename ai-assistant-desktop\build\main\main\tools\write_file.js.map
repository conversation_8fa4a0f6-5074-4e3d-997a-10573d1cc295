{"version": 3, "file": "write_file.js", "sourceRoot": "", "sources": ["../../../../src/main/tools/write_file.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,0BA+EC;AAGD,oCA6DC;AAlMD,gDAAkC;AAClC,2CAA6B;AAE7B,iDAAoD;AAEvC,QAAA,QAAQ,GAAG,2BAAe,CAAC,WAAW,CAAC;AAEvC,QAAA,MAAM,GAAe;IAChC,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,wHAAwH;IACrI,QAAQ,EAAE,2BAAe,CAAC,WAAW;IACrC,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,uDAAuD;aACrE;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kCAAkC;aAChD;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,wEAAwE;gBACrF,OAAO,EAAE,MAAM;aAChB;YACD,iBAAiB,EAAE;gBACjB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,gEAAgE;gBAC7E,OAAO,EAAE,IAAI;aACd;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,qDAAqD;gBAClE,OAAO,EAAE,IAAI;aACd;SACF;QACD,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;KAC9B;IACD,QAAQ,EAAE;QACR;YACE,WAAW,EAAE,mBAAmB;YAChC,SAAS,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE;YAC7D,cAAc,EAAE,2BAA2B;SAC5C;KACF;IACD,SAAS,EAAE,QAAQ;IACnB,oBAAoB,EAAE,IAAI;CAC3B,CAAC;AAEK,KAAK,UAAU,OAAO,CAAC,IAM7B;IAQC,MAAM,EACJ,IAAI,EAAE,QAAQ,EACd,OAAO,EACP,QAAQ,GAAG,MAAM,EACjB,iBAAiB,GAAG,IAAI,EACxB,SAAS,GAAG,IAAI,EACjB,GAAG,IAAI,CAAC;IAET,IAAI,CAAC;QACH,2BAA2B;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5C,uBAAuB;QACvB,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC9B,OAAO,GAAG,IAAI,CAAC;YAEf,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,kDAAkD,YAAY,EAAE,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,gBAAgB;QAChB,IAAI,YAAoB,CAAC;QACzB,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC1B,wBAAwB;YACxB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC9C,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACzC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,QAA0B,EAAE,CAAC,CAAC;YACpF,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,QAA0B,CAAC,CAAC;QACxE,CAAC;QAED,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,YAAY;YACZ,YAAY;YACZ,OAAO,EAAE,CAAC,OAAO;YACjB,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;QACpD,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;QACjE,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;AACH,CAAC;AAED,oCAAoC;AAC7B,KAAK,UAAU,YAAY,CAAC,IAKlC;IAOC,MAAM,EACJ,IAAI,EAAE,QAAQ,EACd,OAAO,EACP,QAAQ,GAAG,MAAM,EACjB,iBAAiB,GAAG,IAAI,EACzB,GAAG,IAAI,CAAC;IAET,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5C,uBAAuB;QACvB,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,YAAY,EAAE,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,IAAI,aAAqB,CAAC;QAC1B,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC9C,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC1C,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,QAA0B,EAAE,CAAC,CAAC;YACrF,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,QAA0B,CAAC,CAAC;QACzE,CAAC;QAED,sBAAsB;QACtB,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1C,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,YAAY;YACZ,aAAa;YACb,QAAQ,EAAE,KAAK,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC"}