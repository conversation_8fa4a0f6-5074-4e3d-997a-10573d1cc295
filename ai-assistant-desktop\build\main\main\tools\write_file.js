"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
exports.appendToFile = appendToFile;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const constants_1 = require("@shared/constants");
exports.category = constants_1.TOOL_CATEGORIES.FILE_SYSTEM;
exports.schema = {
    name: 'write_file',
    description: 'Write content to a file on the filesystem. Creates directories if they do not exist. Supports text and binary content.',
    category: constants_1.TOOL_CATEGORIES.FILE_SYSTEM,
    parameters: {
        type: 'object',
        properties: {
            path: {
                type: 'string',
                description: 'The absolute or relative path where to write the file',
            },
            content: {
                type: 'string',
                description: 'The content to write to the file',
            },
            encoding: {
                type: 'string',
                description: 'Text encoding to use (default: utf8). Use "base64" for binary content.',
                default: 'utf8',
            },
            createDirectories: {
                type: 'boolean',
                description: 'Create parent directories if they do not exist (default: true)',
                default: true,
            },
            overwrite: {
                type: 'boolean',
                description: 'Overwrite file if it already exists (default: true)',
                default: true,
            },
        },
        required: ['path', 'content'],
    },
    examples: [
        {
            description: 'Write a text file',
            arguments: { path: './output.txt', content: 'Hello, World!' },
            expectedOutput: 'File written successfully',
        },
    ],
    riskLevel: 'medium',
    requiresConfirmation: true,
};
async function execute(args) {
    const { path: filePath, content, encoding = 'utf8', createDirectories = true, overwrite = true } = args;
    try {
        // Resolve to absolute path
        const absolutePath = path.resolve(filePath);
        // Check if file exists
        let existed = false;
        try {
            await fs.access(absolutePath);
            existed = true;
            if (!overwrite) {
                throw new Error(`File already exists and overwrite is disabled: ${absolutePath}`);
            }
        }
        catch (error) {
            if (error.code !== 'ENOENT') {
                throw error;
            }
        }
        // Create parent directories if needed
        if (createDirectories) {
            const dirPath = path.dirname(absolutePath);
            await fs.mkdir(dirPath, { recursive: true });
        }
        // Write content
        let bytesWritten;
        if (encoding === 'base64') {
            // Handle binary content
            const buffer = Buffer.from(content, 'base64');
            await fs.writeFile(absolutePath, buffer);
            bytesWritten = buffer.length;
        }
        else {
            // Handle text content
            await fs.writeFile(absolutePath, content, { encoding: encoding });
            bytesWritten = Buffer.byteLength(content, encoding);
        }
        return {
            path: filePath,
            absolutePath,
            bytesWritten,
            created: !existed,
            encoding,
            timestamp: new Date().toISOString(),
        };
    }
    catch (error) {
        if (error.code === 'EACCES') {
            throw new Error(`Permission denied: ${filePath}`);
        }
        else if (error.code === 'ENOTDIR') {
            throw new Error(`Parent path is not a directory: ${filePath}`);
        }
        else if (error.code === 'ENOSPC') {
            throw new Error(`No space left on device: ${filePath}`);
        }
        else {
            throw new Error(`Error writing file: ${error.message}`);
        }
    }
}
// Helper function to append to file
async function appendToFile(args) {
    const { path: filePath, content, encoding = 'utf8', createIfNotExists = true } = args;
    try {
        const absolutePath = path.resolve(filePath);
        // Check if file exists
        let existed = true;
        try {
            await fs.access(absolutePath);
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                existed = false;
                if (!createIfNotExists) {
                    throw new Error(`File does not exist: ${absolutePath}`);
                }
            }
            else {
                throw error;
            }
        }
        // Append content
        let bytesAppended;
        if (encoding === 'base64') {
            const buffer = Buffer.from(content, 'base64');
            await fs.appendFile(absolutePath, buffer);
            bytesAppended = buffer.length;
        }
        else {
            await fs.appendFile(absolutePath, content, { encoding: encoding });
            bytesAppended = Buffer.byteLength(content, encoding);
        }
        // Get final file size
        const stats = await fs.stat(absolutePath);
        return {
            path: filePath,
            absolutePath,
            bytesAppended,
            fileSize: stats.size,
            timestamp: new Date().toISOString(),
        };
    }
    catch (error) {
        throw new Error(`Error appending to file: ${error.message}`);
    }
}
//# sourceMappingURL=write_file.js.map