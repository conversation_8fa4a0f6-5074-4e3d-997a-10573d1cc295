"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystemService = void 0;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const glob_1 = require("glob");
class FileSystemService {
    async readFile(filePath) {
        try {
            const absolutePath = path.resolve(filePath);
            const content = await fs.readFile(absolutePath, 'utf-8');
            return content;
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`File not found: ${filePath}`);
            }
            throw new Error(`Error reading file: ${error.message}`);
        }
    }
    async writeFile(filePath, content) {
        try {
            const absolutePath = path.resolve(filePath);
            const dirPath = path.dirname(absolutePath);
            // Ensure directory exists
            await fs.mkdir(dirPath, { recursive: true });
            await fs.writeFile(absolutePath, content, 'utf-8');
        }
        catch (error) {
            throw new Error(`Error writing file: ${error.message}`);
        }
    }
    async listDirectory(dirPath) {
        try {
            const absolutePath = path.resolve(dirPath);
            const entries = await fs.readdir(absolutePath, { withFileTypes: true });
            const files = [];
            for (const entry of entries) {
                const fullPath = path.join(absolutePath, entry.name);
                const stats = await fs.stat(fullPath);
                const fileInfo = {
                    path: fullPath,
                    name: entry.name,
                    type: entry.isDirectory() ? 'directory' : 'file',
                    size: entry.isFile() ? stats.size : undefined,
                    modified: stats.mtime,
                    extension: entry.isFile() ? path.extname(entry.name).toLowerCase() : undefined,
                };
                files.push(fileInfo);
            }
            return files;
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`Directory not found: ${dirPath}`);
            }
            throw new Error(`Error listing directory: ${error.message}`);
        }
    }
    async globFiles(pattern, options) {
        try {
            const { cwd = process.cwd(), absolute = true, maxMatches = 1000 } = options || {};
            let matches = await (0, glob_1.glob)(pattern, {
                cwd,
                absolute,
            });
            // Apply maxMatches limit manually
            if (maxMatches && matches.length > maxMatches) {
                matches = matches.slice(0, maxMatches);
            }
            return matches;
        }
        catch (error) {
            throw new Error(`Error globbing files: ${error.message}`);
        }
    }
    async exists(filePath) {
        try {
            await fs.access(path.resolve(filePath));
            return true;
        }
        catch {
            return false;
        }
    }
    async getStats(filePath) {
        try {
            const stats = await fs.stat(path.resolve(filePath));
            return {
                size: stats.size,
                isFile: stats.isFile(),
                isDirectory: stats.isDirectory(),
                modified: stats.mtime,
                created: stats.birthtime,
            };
        }
        catch (error) {
            throw new Error(`Error getting file stats: ${error.message}`);
        }
    }
    async deleteFile(filePath) {
        try {
            await fs.unlink(path.resolve(filePath));
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`File not found: ${filePath}`);
            }
            throw new Error(`Error deleting file: ${error.message}`);
        }
    }
    async createDirectory(dirPath, recursive = true) {
        try {
            await fs.mkdir(path.resolve(dirPath), { recursive });
        }
        catch (error) {
            throw new Error(`Error creating directory: ${error.message}`);
        }
    }
    async copyFile(source, destination) {
        try {
            const absoluteSource = path.resolve(source);
            const absoluteDestination = path.resolve(destination);
            // Ensure destination directory exists
            const destDir = path.dirname(absoluteDestination);
            await fs.mkdir(destDir, { recursive: true });
            await fs.copyFile(absoluteSource, absoluteDestination);
        }
        catch (error) {
            throw new Error(`Error copying file: ${error.message}`);
        }
    }
    async moveFile(source, destination) {
        try {
            const absoluteSource = path.resolve(source);
            const absoluteDestination = path.resolve(destination);
            // Ensure destination directory exists
            const destDir = path.dirname(absoluteDestination);
            await fs.mkdir(destDir, { recursive: true });
            await fs.rename(absoluteSource, absoluteDestination);
        }
        catch (error) {
            throw new Error(`Error moving file: ${error.message}`);
        }
    }
    // Diff utilities for file comparison
    async createDiff(oldContent, newContent, oldPath, newPath) {
        const oldLines = oldContent.split('\n');
        const newLines = newContent.split('\n');
        // Simple diff algorithm (could be enhanced with a proper diff library)
        const hunks = [];
        let oldIndex = 0;
        let newIndex = 0;
        while (oldIndex < oldLines.length || newIndex < newLines.length) {
            const hunkOldStart = oldIndex;
            const hunkNewStart = newIndex;
            const hunkLines = [];
            // Find differences
            let foundDiff = false;
            let linesProcessed = 0;
            while ((oldIndex < oldLines.length || newIndex < newLines.length) && linesProcessed < 50) {
                const oldLine = oldIndex < oldLines.length ? oldLines[oldIndex] : undefined;
                const newLine = newIndex < newLines.length ? newLines[newIndex] : undefined;
                if (oldLine === newLine) {
                    if (foundDiff || hunkLines.length === 0) {
                        hunkLines.push({
                            type: 'normal',
                            content: oldLine || '',
                            oldNumber: oldIndex + 1,
                            newNumber: newIndex + 1,
                        });
                    }
                    oldIndex++;
                    newIndex++;
                }
                else if (oldLine && !newLine) {
                    // Line deleted
                    hunkLines.push({
                        type: 'delete',
                        content: oldLine,
                        oldNumber: oldIndex + 1,
                    });
                    oldIndex++;
                    foundDiff = true;
                }
                else if (!oldLine && newLine) {
                    // Line added
                    hunkLines.push({
                        type: 'add',
                        content: newLine,
                        newNumber: newIndex + 1,
                    });
                    newIndex++;
                    foundDiff = true;
                }
                else {
                    // Line changed - treat as delete + add
                    hunkLines.push({
                        type: 'delete',
                        content: oldLine,
                        oldNumber: oldIndex + 1,
                    });
                    hunkLines.push({
                        type: 'add',
                        content: newLine,
                        newNumber: newIndex + 1,
                    });
                    oldIndex++;
                    newIndex++;
                    foundDiff = true;
                }
                linesProcessed++;
                // If we have context and no more diffs nearby, break
                if (foundDiff && oldLine === newLine && linesProcessed > 3) {
                    break;
                }
            }
            if (hunkLines.length > 0) {
                const hunk = {
                    oldStart: hunkOldStart + 1,
                    oldLines: oldIndex - hunkOldStart,
                    newStart: hunkNewStart + 1,
                    newLines: newIndex - hunkNewStart,
                    lines: hunkLines,
                };
                hunks.push(hunk);
            }
            if (!foundDiff) {
                break;
            }
        }
        return {
            oldPath: oldPath || 'old',
            newPath: newPath || 'new',
            hunks,
        };
    }
    async replaceInFile(filePath, searchText, replaceText, options) {
        const { regex = false, global = true, caseSensitive = true, createBackup = true, } = options || {};
        try {
            const absolutePath = path.resolve(filePath);
            const originalContent = await this.readFile(absolutePath);
            let newContent;
            let replacements = 0;
            if (regex) {
                const flags = `${global ? 'g' : ''}${caseSensitive ? '' : 'i'}`;
                const regexPattern = new RegExp(searchText, flags);
                const matches = originalContent.match(regexPattern);
                replacements = matches ? matches.length : 0;
                newContent = originalContent.replace(regexPattern, replaceText);
            }
            else {
                // Simple text replacement
                if (global) {
                    const searchPattern = caseSensitive ? searchText : searchText.toLowerCase();
                    const contentToSearch = caseSensitive ? originalContent : originalContent.toLowerCase();
                    let startIndex = 0;
                    let tempContent = originalContent;
                    while (true) {
                        const index = contentToSearch.indexOf(searchPattern, startIndex);
                        if (index === -1)
                            break;
                        tempContent = tempContent.substring(0, index) +
                            replaceText +
                            tempContent.substring(index + searchText.length);
                        startIndex = index + replaceText.length;
                        replacements++;
                    }
                    newContent = tempContent;
                }
                else {
                    // Single replacement
                    const index = caseSensitive
                        ? originalContent.indexOf(searchText)
                        : originalContent.toLowerCase().indexOf(searchText.toLowerCase());
                    if (index !== -1) {
                        newContent = originalContent.substring(0, index) +
                            replaceText +
                            originalContent.substring(index + searchText.length);
                        replacements = 1;
                    }
                    else {
                        newContent = originalContent;
                    }
                }
            }
            // Create backup if requested
            let backupPath;
            if (createBackup && replacements > 0) {
                backupPath = `${absolutePath}.backup.${Date.now()}`;
                await fs.copyFile(absolutePath, backupPath);
            }
            // Generate preview diff
            const preview = await this.createDiff(originalContent, newContent, filePath, filePath);
            // Write new content
            if (replacements > 0) {
                await this.writeFile(absolutePath, newContent);
            }
            return {
                success: true,
                replacements,
                preview,
                backupPath,
            };
        }
        catch (error) {
            throw new Error(`Error replacing in file: ${error.message}`);
        }
    }
    // Watch file changes (basic implementation)
    watchFile(filePath, callback) {
        const watcher = fs.watch(path.resolve(filePath), { encoding: 'utf8' }, callback);
        return () => watcher.close();
    }
}
exports.FileSystemService = FileSystemService;
//# sourceMappingURL=FileSystemService.js.map