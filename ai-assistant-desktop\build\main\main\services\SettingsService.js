"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const crypto = __importStar(require("crypto-js"));
const constants_1 = require("@shared/constants");
class SettingsService {
    constructor(databaseService) {
        this.databaseService = databaseService;
        this.ENCRYPTION_KEY = 'ai-assistant-encryption-key';
        this.cachedSettings = null;
        // Event emitters for settings changes (optional)
        this.listeners = new Map();
    }
    async initialize() {
        // Load settings from database
        await this.loadSettings();
    }
    async getSettings() {
        if (!this.cachedSettings) {
            await this.loadSettings();
        }
        return this.cachedSettings;
    }
    async updateSettings(updates) {
        const currentSettings = await this.getSettings();
        const newSettings = this.deepMerge(currentSettings, updates);
        // Encrypt sensitive data before storing
        const settingsToStore = this.encryptSensitiveData(newSettings);
        await this.databaseService.updateSettings(settingsToStore);
        this.cachedSettings = newSettings;
    }
    async getSetting(key) {
        const settings = await this.getSettings();
        return settings[key];
    }
    async setSetting(key, value) {
        await this.updateSettings({ [key]: value });
    }
    async resetSettings() {
        await this.databaseService.updateSettings(constants_1.DEFAULT_SETTINGS);
        this.cachedSettings = { ...constants_1.DEFAULT_SETTINGS };
    }
    // LLM Provider specific methods
    async getLLMConfig() {
        const settings = await this.getSettings();
        return settings.llm;
    }
    async updateLLMConfig(config) {
        await this.updateSettings({ llm: config });
    }
    async setAPIKey(provider, apiKey) {
        const currentLLM = await this.getLLMConfig();
        if (currentLLM.provider === provider) {
            await this.updateLLMConfig({ apiKey });
        }
    }
    async getAPIKey(provider) {
        const llmConfig = await this.getLLMConfig();
        if (!provider || llmConfig.provider === provider) {
            return llmConfig.apiKey;
        }
        return undefined;
    }
    // UI Settings
    async getTheme() {
        const settings = await this.getSettings();
        return settings.ui.theme;
    }
    async setTheme(theme) {
        await this.updateSettings({
            ui: { theme }
        });
    }
    async getFontSize() {
        const settings = await this.getSettings();
        return settings.ui.fontSize;
    }
    async setFontSize(fontSize) {
        await this.updateSettings({
            ui: { fontSize }
        });
    }
    async getCompactMode() {
        const settings = await this.getSettings();
        return settings.ui.compactMode;
    }
    async setCompactMode(compactMode) {
        await this.updateSettings({
            ui: { compactMode }
        });
    }
    // Agent Settings
    async getExecutionMode() {
        const settings = await this.getSettings();
        return settings.agent.defaultExecutionMode;
    }
    async setExecutionMode(mode) {
        await this.updateSettings({
            agent: { defaultExecutionMode: mode }
        });
    }
    async getAutoSaveConversations() {
        const settings = await this.getSettings();
        return settings.agent.autoSaveConversations;
    }
    async setAutoSaveConversations(autoSave) {
        await this.updateSettings({
            agent: { autoSaveConversations: autoSave }
        });
    }
    async getMaxContextLength() {
        const settings = await this.getSettings();
        return settings.agent.maxContextLength;
    }
    async setMaxContextLength(maxLength) {
        await this.updateSettings({
            agent: { maxContextLength: maxLength }
        });
    }
    // Tool Settings
    async getEnabledTools() {
        const settings = await this.getSettings();
        return settings.tools.enabledTools;
    }
    async setEnabledTools(tools) {
        await this.updateSettings({
            tools: { enabledTools: tools }
        });
    }
    async enableTool(toolName) {
        const currentTools = await this.getEnabledTools();
        if (!currentTools.includes(toolName)) {
            await this.setEnabledTools([...currentTools, toolName]);
        }
    }
    async disableTool(toolName) {
        const currentTools = await this.getEnabledTools();
        const filteredTools = currentTools.filter(tool => tool !== toolName);
        await this.setEnabledTools(filteredTools);
    }
    async getToolSettings() {
        const settings = await this.getSettings();
        return settings.tools.toolSettings;
    }
    async setToolSettings(toolSettings) {
        await this.updateSettings({
            tools: { toolSettings }
        });
    }
    async getToolSetting(toolName) {
        const toolSettings = await this.getToolSettings();
        return toolSettings[toolName];
    }
    async setToolSetting(toolName, setting) {
        const currentToolSettings = await this.getToolSettings();
        await this.setToolSettings({
            ...currentToolSettings,
            [toolName]: setting,
        });
    }
    // Import/Export functionality
    async exportSettings() {
        const settings = await this.getSettings();
        // Remove sensitive data from export
        const exportData = {
            ...settings,
            llm: {
                ...settings.llm,
                apiKey: '', // Don't export API keys
            },
        };
        return JSON.stringify(exportData, null, 2);
    }
    async importSettings(settingsJson) {
        try {
            const importedSettings = JSON.parse(settingsJson);
            // Validate imported settings structure
            if (!this.validateSettingsStructure(importedSettings)) {
                throw new Error('Invalid settings format');
            }
            // Preserve current API key if not provided in import
            const currentSettings = await this.getSettings();
            if (!importedSettings.llm?.apiKey && currentSettings.llm.apiKey) {
                importedSettings.llm = {
                    ...importedSettings.llm,
                    apiKey: currentSettings.llm.apiKey,
                };
            }
            await this.updateSettings(importedSettings);
        }
        catch (error) {
            throw new Error(`Failed to import settings: ${error.message}`);
        }
    }
    // Validation methods
    validateLLMConfig(config) {
        const errors = [];
        if (!config.provider) {
            errors.push('LLM provider is required');
        }
        else if (!['openai', 'anthropic', 'deepseek'].includes(config.provider)) {
            errors.push('Invalid LLM provider');
        }
        if (!config.model || config.model.trim() === '') {
            errors.push('LLM model is required');
        }
        if (!config.apiKey || config.apiKey.trim() === '') {
            errors.push('API key is required');
        }
        if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 128000)) {
            errors.push('Max tokens must be between 1 and 128000');
        }
        if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
            errors.push('Temperature must be between 0 and 2');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    async loadSettings() {
        try {
            const settingsFromDb = this.databaseService.getSettings();
            const decryptedSettings = this.decryptSensitiveData(settingsFromDb);
            this.cachedSettings = decryptedSettings;
        }
        catch (error) {
            console.error('Failed to load settings, using defaults:', error);
            this.cachedSettings = { ...constants_1.DEFAULT_SETTINGS };
        }
    }
    validateSettingsStructure(settings) {
        try {
            // Basic structure validation
            if (typeof settings !== 'object' || settings === null) {
                return false;
            }
            // Validate top-level keys
            const requiredKeys = ['llm', 'ui', 'agent', 'tools'];
            for (const key of requiredKeys) {
                if (!(key in settings) || typeof settings[key] !== 'object') {
                    return false;
                }
            }
            // Validate LLM config
            if (settings.llm) {
                const llmKeys = ['provider', 'model'];
                for (const key of llmKeys) {
                    if (key in settings.llm && typeof settings.llm[key] !== 'string') {
                        return false;
                    }
                }
            }
            // Validate UI config
            if (settings.ui) {
                if ('theme' in settings.ui && !['light', 'dark', 'system'].includes(settings.ui.theme)) {
                    return false;
                }
                if ('fontSize' in settings.ui && !['small', 'medium', 'large'].includes(settings.ui.fontSize)) {
                    return false;
                }
                if ('compactMode' in settings.ui && typeof settings.ui.compactMode !== 'boolean') {
                    return false;
                }
            }
            // Validate agent config
            if (settings.agent) {
                if ('defaultExecutionMode' in settings.agent &&
                    !['confirm', 'yolo'].includes(settings.agent.defaultExecutionMode)) {
                    return false;
                }
                if ('autoSaveConversations' in settings.agent &&
                    typeof settings.agent.autoSaveConversations !== 'boolean') {
                    return false;
                }
                if ('maxContextLength' in settings.agent &&
                    (typeof settings.agent.maxContextLength !== 'number' || settings.agent.maxContextLength < 1)) {
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    encryptSensitiveData(settings) {
        const encrypted = { ...settings };
        // Encrypt API key
        if (encrypted.llm.apiKey) {
            encrypted.llm.apiKey = this.encrypt(encrypted.llm.apiKey);
        }
        return encrypted;
    }
    decryptSensitiveData(settings) {
        const decrypted = { ...settings };
        // Decrypt API key
        if (decrypted.llm.apiKey) {
            try {
                decrypted.llm.apiKey = this.decrypt(decrypted.llm.apiKey);
            }
            catch (error) {
                console.warn('Failed to decrypt API key, using empty string');
                decrypted.llm.apiKey = '';
            }
        }
        return decrypted;
    }
    encrypt(text) {
        return crypto.AES.encrypt(text, this.ENCRYPTION_KEY).toString();
    }
    decrypt(encryptedText) {
        const bytes = crypto.AES.decrypt(encryptedText, this.ENCRYPTION_KEY);
        return bytes.toString(crypto.enc.Utf8);
    }
    deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                const sourceValue = source[key];
                const targetValue = result[key];
                if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue) &&
                    targetValue && typeof targetValue === 'object' && !Array.isArray(targetValue)) {
                    // Recursively merge objects
                    result[key] = this.deepMerge(targetValue, sourceValue);
                }
                else {
                    // Direct assignment for primitives and arrays
                    result[key] = sourceValue;
                }
            }
        }
        return result;
    }
    onSettingChanged(key, callback) {
        const keyStr = key;
        if (!this.listeners.has(keyStr)) {
            this.listeners.set(keyStr, new Set());
        }
        this.listeners.get(keyStr).add(callback);
        // Return unsubscribe function
        return () => {
            this.listeners.get(keyStr)?.delete(callback);
        };
    }
    emitSettingChanged(key, value) {
        const keyStr = key;
        const callbacks = this.listeners.get(keyStr);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(value);
                }
                catch (error) {
                    console.error('Error in settings change callback:', error);
                }
            });
        }
    }
}
exports.SettingsService = SettingsService;
//# sourceMappingURL=SettingsService.js.map