"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const path = __importStar(require("path"));
const glob_1 = require("glob");
const ajv_1 = __importDefault(require("ajv"));
class ToolRegistry {
    constructor() {
        this.tools = new Map();
        this.ajv = new ajv_1.default({ allErrors: true });
        this.toolsDirectory = path.join(__dirname, '../tools');
    }
    async initialize() {
        await this.loadTools();
        console.log(`Loaded ${this.tools.size} tools:`, Array.from(this.tools.keys()));
    }
    async loadTools() {
        try {
            // Find all tool files
            const toolFiles = await (0, glob_1.glob)('*.ts', {
                cwd: this.toolsDirectory,
                absolute: true
            });
            // Load each tool
            for (const toolFile of toolFiles) {
                try {
                    await this.loadTool(toolFile);
                }
                catch (error) {
                    console.error(`Failed to load tool from ${toolFile}:`, error);
                }
            }
        }
        catch (error) {
            console.error('Failed to scan tools directory:', error);
        }
    }
    async loadTool(toolPath) {
        try {
            // Import the tool module
            const toolModule = await Promise.resolve(`${toolPath}`).then(s => __importStar(require(s)));
            // Each tool should export: schema, execute, and optionally category
            if (!toolModule.schema || !toolModule.execute) {
                throw new Error('Tool must export schema and execute function');
            }
            const schema = toolModule.schema;
            const execute = toolModule.execute;
            const category = toolModule.category || 'utility';
            // Validate schema structure
            if (!this.validateToolSchema(schema)) {
                throw new Error(`Invalid tool schema for ${schema.name}`);
            }
            const toolDefinition = {
                name: schema.name,
                description: schema.description,
                category,
                schema,
                execute,
            };
            this.tools.set(schema.name, toolDefinition);
            console.log(`Loaded tool: ${schema.name} (${category})`);
        }
        catch (error) {
            console.error(`Error loading tool from ${toolPath}:`, error);
            throw error;
        }
    }
    validateToolSchema(schema) {
        return !!(schema.name &&
            schema.description &&
            Array.isArray(schema.parameters));
    }
    async executeTool(name, args) {
        const tool = this.tools.get(name);
        if (!tool) {
            return {
                id: this.generateId(),
                toolName: name,
                name,
                input: args,
                output: null,
                result: null,
                status: 'error',
                success: false,
                error: `Tool '${name}' not found`,
                timestamp: new Date(),
            };
        }
        // Validate arguments against schema
        const validationResult = this.validateArguments(tool.schema, args);
        if (!validationResult.valid) {
            return {
                id: this.generateId(),
                toolName: name,
                name,
                input: args,
                output: null,
                result: null,
                status: 'error',
                success: false,
                error: `Invalid arguments: ${validationResult.errors.join(', ')}`,
                timestamp: new Date(),
            };
        }
        try {
            const startTime = Date.now();
            const result = await tool.execute(args);
            const duration = Date.now() - startTime;
            return {
                id: this.generateId(),
                toolName: name,
                name,
                input: args,
                output: result,
                result,
                status: 'success',
                success: true,
                timestamp: new Date(),
                duration,
                metadata: {
                    duration,
                    timestamp: new Date().toISOString(),
                },
            };
        }
        catch (error) {
            return {
                id: this.generateId(),
                toolName: name,
                name,
                input: args,
                output: null,
                result: null,
                status: 'error',
                success: false,
                error: error.message || 'Tool execution failed',
                timestamp: new Date(),
                metadata: {
                    timestamp: new Date().toISOString(),
                },
            };
        }
    }
    validateArguments(schema, args) {
        const errors = [];
        // Check required parameters
        if (schema.parameters.required) {
            for (const paramName of schema.parameters.required) {
                if (!(paramName in args)) {
                    errors.push(`Missing required parameter: ${paramName}`);
                }
            }
        }
        // Validate parameter types
        if (schema.parameters.properties) {
            for (const [paramName, paramDef] of Object.entries(schema.parameters.properties)) {
                if (paramName in args) {
                    const value = args[paramName];
                    if (!this.validateParameterType(paramDef, value)) {
                        errors.push(`Invalid type for parameter ${paramName}: expected ${paramDef.type}`);
                    }
                }
            }
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    validateParameterType(param, value) {
        switch (param.type) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number';
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return true;
        }
    }
    getAvailableTools() {
        return Array.from(this.tools.values());
    }
    getToolsByCategory(category) {
        return Array.from(this.tools.values()).filter(tool => tool.category === category);
    }
    getToolSchema(name) {
        const tool = this.tools.get(name);
        return tool ? tool.schema : null;
    }
    getToolSchemas() {
        return Array.from(this.tools.values()).map(tool => tool.schema);
    }
    generateSystemPrompt() {
        const tools = this.getAvailableTools();
        if (tools.length === 0) {
            return 'No tools are currently available.';
        }
        let prompt = 'You have access to the following tools:\n\n';
        // Group tools by category
        const categories = new Map();
        tools.forEach(tool => {
            if (!categories.has(tool.category)) {
                categories.set(tool.category, []);
            }
            categories.get(tool.category).push(tool);
        });
        // Generate prompt for each category
        categories.forEach((categoryTools, category) => {
            prompt += `## ${category.toUpperCase()} TOOLS\n\n`;
            categoryTools.forEach(tool => {
                prompt += `### ${tool.name}\n`;
                prompt += `${tool.description}\n\n`;
                if (tool.schema.parameters.properties && Object.keys(tool.schema.parameters.properties).length > 0) {
                    prompt += 'Parameters:\n';
                    Object.entries(tool.schema.parameters.properties).forEach(([paramName, paramDef]) => {
                        const required = tool.schema.parameters.required?.includes(paramName) ? ' (required)' : ' (optional)';
                        const defaultVal = paramDef.default !== undefined ? ` [default: ${paramDef.default}]` : '';
                        prompt += `- ${paramName} (${paramDef.type})${required}${defaultVal}: ${paramDef.description}\n`;
                    });
                }
                else {
                    prompt += 'No parameters required.\n';
                }
                prompt += '\n';
            });
        });
        prompt += `
When using tools, always:
1. Provide all required parameters
2. Use appropriate parameter types
3. Handle tool results appropriately
4. Explain what you're doing before executing tools

Tools should be called as JSON objects with the following structure:
{
  "name": "tool_name",
  "arguments": {
    "param1": "value1",
    "param2": "value2"
  }
}
`;
        return prompt;
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // Tool management methods
    registerTool(toolDefinition) {
        if (!this.validateToolSchema(toolDefinition.schema)) {
            throw new Error(`Invalid tool schema for ${toolDefinition.name}`);
        }
        this.tools.set(toolDefinition.name, toolDefinition);
        console.log(`Registered tool: ${toolDefinition.name}`);
    }
    unregisterTool(name) {
        return this.tools.delete(name);
    }
    isToolAvailable(name) {
        return this.tools.has(name);
    }
    getToolCategories() {
        const categories = new Set();
        this.tools.forEach(tool => categories.add(tool.category));
        return Array.from(categories);
    }
    // Enable/disable tools based on settings
    filterToolsBySettings(enabledTools) {
        return Array.from(this.tools.values()).filter(tool => enabledTools.includes(tool.name));
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=ToolRegistry.js.map