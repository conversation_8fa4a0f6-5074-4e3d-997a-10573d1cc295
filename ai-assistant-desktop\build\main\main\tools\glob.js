"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
const glob_1 = require("glob");
const path = __importStar(require("path"));
const fs = __importStar(require("fs/promises"));
const constants_1 = require("@shared/constants");
exports.category = constants_1.TOOL_CATEGORIES.FILE_SYSTEM;
exports.schema = {
    name: 'glob',
    description: 'Find files and directories using glob patterns. Supports wildcards, character classes, and advanced pattern matching.',
    category: constants_1.TOOL_CATEGORIES.FILE_SYSTEM,
    parameters: {
        type: 'object',
        properties: {
            pattern: {
                type: 'string',
                description: 'Glob pattern to match files/directories (e.g., "**/*.ts", "src/**/*.{js,ts}", "*.json")',
            },
            cwd: {
                type: 'string',
                description: 'Working directory to search from (default: current directory)',
            },
            includeDirectories: {
                type: 'boolean',
                description: 'Whether to include directories in results (default: false)',
                default: false,
            },
            followSymlinks: {
                type: 'boolean',
                description: 'Whether to follow symbolic links (default: false)',
                default: false,
            },
            maxResults: {
                type: 'number',
                description: 'Maximum number of results to return (default: 1000)',
                default: 1000,
            },
            ignorePatterns: {
                type: 'array',
                description: 'Array of patterns to ignore (e.g., ["node_modules/**", "*.log"])',
            },
        },
        required: ['pattern'],
    },
    examples: [
        {
            description: 'Find all TypeScript files',
            arguments: { pattern: '**/*.ts' },
            expectedOutput: 'List of TypeScript files',
        },
    ],
    riskLevel: 'low',
};
async function execute(args) {
    const { pattern, cwd = process.cwd(), includeDirectories = false, followSymlinks = false, maxResults = 1000, ignorePatterns = [], } = args;
    try {
        // Resolve working directory
        const searchDirectory = path.resolve(cwd);
        // Validate search directory exists
        try {
            await fs.access(searchDirectory);
        }
        catch {
            return {
                success: false,
                message: `Search directory not found: ${cwd}`,
                results: {
                    files: [],
                    totalFound: 0,
                    pattern,
                    searchDirectory,
                },
                error: 'Directory not found',
            };
        }
        // Configure glob options
        const globOptions = {
            cwd: searchDirectory,
            absolute: true,
            followSymbolicLinks: followSymlinks,
            ignore: [
                'node_modules/**',
                '.git/**',
                '.vscode/**',
                'dist/**',
                'build/**',
                '*.log',
                '.DS_Store',
                'Thumbs.db',
                ...ignorePatterns,
            ],
        };
        // Execute glob search
        const matches = await (0, glob_1.glob)(pattern, globOptions);
        // Process results
        const files = [];
        let processedCount = 0;
        for (const match of matches) {
            if (processedCount >= maxResults)
                break;
            try {
                const stats = await fs.stat(match);
                const isDirectory = stats.isDirectory();
                // Skip directories if not requested
                if (isDirectory && !includeDirectories) {
                    continue;
                }
                const relativePath = path.relative(searchDirectory, match);
                files.push({
                    path: match,
                    relativePath,
                    isDirectory,
                    size: isDirectory ? undefined : stats.size,
                    modified: stats.mtime.toISOString(),
                });
                processedCount++;
            }
            catch (error) {
                // Skip files that can't be accessed
                console.warn(`Could not access file: ${match}`, error);
                continue;
            }
        }
        // Sort results: directories first, then files, both alphabetically
        files.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory)
                return -1;
            if (!a.isDirectory && b.isDirectory)
                return 1;
            return a.relativePath.localeCompare(b.relativePath);
        });
        const totalFound = matches.length;
        const message = `Found ${totalFound} matches for pattern "${pattern}"${totalFound > maxResults ? ` (showing first ${maxResults})` : ''}`;
        return {
            success: true,
            message,
            results: {
                files,
                totalFound,
                pattern,
                searchDirectory,
            },
        };
    }
    catch (error) {
        return {
            success: false,
            message: `Glob search failed: ${error.message}`,
            results: {
                files: [],
                totalFound: 0,
                pattern,
                searchDirectory: path.resolve(cwd || process.cwd()),
            },
            error: error.message,
        };
    }
}
// Helper function to format file size
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
}
// Helper function to get file extension
function getFileExtension(filePath) {
    return path.extname(filePath).toLowerCase();
}
// Helper function to categorize files
function categorizeFile(filePath) {
    const ext = getFileExtension(filePath);
    const categories = {
        'Code': ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs', '.php', '.rb', '.go', '.rs'],
        'Web': ['.html', '.css', '.scss', '.sass', '.less'],
        'Config': ['.json', '.yaml', '.yml', '.toml', '.ini', '.conf', '.config'],
        'Documentation': ['.md', '.txt', '.rst', '.adoc'],
        'Image': ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.ico'],
        'Archive': ['.zip', '.tar', '.gz', '.rar', '.7z'],
    };
    for (const [category, extensions] of Object.entries(categories)) {
        if (extensions.includes(ext)) {
            return category;
        }
    }
    return 'Other';
}
//# sourceMappingURL=glob.js.map