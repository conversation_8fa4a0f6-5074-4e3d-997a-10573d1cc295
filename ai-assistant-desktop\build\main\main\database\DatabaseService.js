"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const electron_1 = require("electron");
const constants_1 = require("@shared/constants");
const index_1 = require("@shared/constants/index");
class DatabaseService {
    constructor() {
        this.db = null;
        // Store database in user data directory
        const userDataPath = electron_1.app.getPath('userData');
        this.dbPath = path.join(userDataPath, index_1.DB_CONSTANTS.DATABASE_NAME);
    }
    async initialize() {
        try {
            // Ensure user data directory exists
            const userDataPath = path.dirname(this.dbPath);
            if (!fs.existsSync(userDataPath)) {
                fs.mkdirSync(userDataPath, { recursive: true });
            }
            // Open database connection
            this.db = new better_sqlite3_1.default(this.dbPath);
            // Enable foreign keys and WAL mode for better performance
            this.db.pragma('foreign_keys = ON');
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('synchronous = NORMAL');
            this.db.pragma('cache_size = 1000');
            this.db.pragma('temp_store = memory');
            // Create tables
            await this.createTables();
            // Initialize default settings if not exists
            await this.initializeDefaultSettings();
            // Set up periodic maintenance
            this.setupMaintenance();
            console.log(`Database initialized at: ${this.dbPath}`);
        }
        catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }
    createTables() {
        if (!this.db)
            throw new Error('Database not initialized');
        // Conversations table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        metadata TEXT DEFAULT '{}'
      )
    `);
        // Messages table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        metadata TEXT DEFAULT '{}',
        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
      )
    `);
        // Settings table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
        // Tool results cache table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS tool_results_cache (
        id TEXT PRIMARY KEY,
        tool_name TEXT NOT NULL,
        arguments_hash TEXT NOT NULL,
        result TEXT NOT NULL,
        success INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        expires_at TEXT,
        access_count INTEGER DEFAULT 0,
        last_accessed TEXT NOT NULL
      )
    `);
        // Context cache table for file contents and metadata
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS context_cache (
        id TEXT PRIMARY KEY,
        path TEXT NOT NULL,
        content_hash TEXT NOT NULL,
        content TEXT,
        metadata TEXT DEFAULT '{}',
        created_at TEXT NOT NULL,
        last_modified TEXT NOT NULL,
        access_count INTEGER DEFAULT 0,
        last_accessed TEXT NOT NULL
      )
    `);
        // Logs table for debugging and monitoring
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level TEXT NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error')),
        message TEXT NOT NULL,
        metadata TEXT DEFAULT '{}',
        timestamp TEXT NOT NULL,
        source TEXT
      )
    `);
        // Create indexes for better performance
        this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id);
      CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp);
      CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations (updated_at);
      CREATE INDEX IF NOT EXISTS idx_tool_results_cache_expires ON tool_results_cache (expires_at);
      CREATE INDEX IF NOT EXISTS idx_context_cache_path ON context_cache (path);
      CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs (timestamp);
      CREATE INDEX IF NOT EXISTS idx_logs_level ON logs (level);
    `);
    }
    async initializeDefaultSettings() {
        const existingSettings = this.getSettings();
        if (!existingSettings.llm.apiKey) {
            // Only initialize if settings don't exist
            await this.updateSettings(constants_1.DEFAULT_SETTINGS);
        }
    }
    // Conversation Management
    async createConversation(data) {
        if (!this.db)
            throw new Error('Database not initialized');
        const id = this.generateId();
        const now = new Date().toISOString();
        const { title, metadata = {} } = data;
        const stmt = this.db.prepare(`
      INSERT INTO conversations (id, title, created_at, updated_at, metadata)
      VALUES (?, ?, ?, ?, ?)
    `);
        stmt.run(id, title, now, now, JSON.stringify(metadata));
        return {
            id,
            title,
            messages: [],
            createdAt: new Date(now),
            updatedAt: new Date(now),
            metadata,
        };
    }
    async getConversation(id) {
        if (!this.db)
            throw new Error('Database not initialized');
        const conversationStmt = this.db.prepare(`
      SELECT * FROM conversations WHERE id = ?
    `);
        const conversationRow = conversationStmt.get(id);
        if (!conversationRow)
            return null;
        const messagesStmt = this.db.prepare(`
      SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC
    `);
        const messageRows = messagesStmt.all(id);
        const messages = messageRows.map(row => ({
            id: row.id,
            role: row.role,
            content: row.content,
            timestamp: new Date(row.timestamp),
            metadata: JSON.parse(row.metadata || '{}'),
        }));
        return {
            id: conversationRow.id,
            title: conversationRow.title,
            messages,
            createdAt: new Date(conversationRow.created_at),
            updatedAt: new Date(conversationRow.updated_at),
            metadata: JSON.parse(conversationRow.metadata || '{}'),
        };
    }
    async getConversations(limit = 50, offset = 0) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      SELECT * FROM conversations 
      ORDER BY updated_at DESC 
      LIMIT ? OFFSET ?
    `);
        const rows = stmt.all(limit, offset);
        const conversations = [];
        for (const row of rows) {
            const conversation = await this.getConversation(row.id);
            if (conversation) {
                conversations.push(conversation);
            }
        }
        return conversations;
    }
    async updateConversation(id, updates) {
        if (!this.db)
            throw new Error('Database not initialized');
        const now = new Date().toISOString();
        const setParts = ['updated_at = ?'];
        const values = [now];
        if (updates.title !== undefined) {
            setParts.push('title = ?');
            values.push(updates.title);
        }
        if (updates.metadata !== undefined) {
            setParts.push('metadata = ?');
            values.push(JSON.stringify(updates.metadata));
        }
        values.push(id);
        const stmt = this.db.prepare(`
      UPDATE conversations 
      SET ${setParts.join(', ')}
      WHERE id = ?
    `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }
    async deleteConversation(id) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('DELETE FROM conversations WHERE id = ?');
        const result = stmt.run(id);
        return result.changes > 0;
    }
    // Message Management
    async addMessage(conversationId, message) {
        if (!this.db)
            throw new Error('Database not initialized');
        const id = this.generateId();
        const messageData = { id, ...message };
        const messageStmt = this.db.prepare(`
      INSERT INTO messages (id, conversation_id, role, content, timestamp, metadata)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
        const conversationStmt = this.db.prepare(`
      UPDATE conversations SET updated_at = ? WHERE id = ?
    `);
        // Use transaction for consistency
        const transaction = this.db.transaction(() => {
            messageStmt.run(id, conversationId, message.role, message.content, message.timestamp.toISOString(), JSON.stringify(message.metadata || {}));
            conversationStmt.run(new Date().toISOString(), conversationId);
        });
        transaction();
        return messageData;
    }
    async updateMessage(id, updates) {
        if (!this.db)
            throw new Error('Database not initialized');
        const setParts = [];
        const values = [];
        if (updates.content !== undefined) {
            setParts.push('content = ?');
            values.push(updates.content);
        }
        if (updates.metadata !== undefined) {
            setParts.push('metadata = ?');
            values.push(JSON.stringify(updates.metadata));
        }
        if (setParts.length === 0)
            return false;
        values.push(id);
        const stmt = this.db.prepare(`
      UPDATE messages 
      SET ${setParts.join(', ')}
      WHERE id = ?
    `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }
    // Settings Management
    getSettings() {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT key, value FROM settings');
        const rows = stmt.all();
        let settings = { ...constants_1.DEFAULT_SETTINGS };
        for (const row of rows) {
            try {
                const value = JSON.parse(row.value);
                this.setNestedValue(settings, row.key, value);
            }
            catch (error) {
                console.warn(`Failed to parse setting ${row.key}:`, error);
            }
        }
        return settings;
    }
    async updateSettings(settings) {
        if (!this.db)
            throw new Error('Database not initialized');
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, ?)
    `);
        const transaction = this.db.transaction(() => {
            this.flattenObject(settings, '', (key, value) => {
                stmt.run(key, JSON.stringify(value), now);
            });
        });
        transaction();
    }
    async getSetting(key) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?');
        const row = stmt.get(key);
        if (!row)
            return undefined;
        try {
            return JSON.parse(row.value);
        }
        catch (error) {
            console.warn(`Failed to parse setting ${key}:`, error);
            return undefined;
        }
    }
    async setSetting(key, value) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, ?)
    `);
        stmt.run(key, JSON.stringify(value), new Date().toISOString());
    }
    // Cache Management
    async cacheToolResult(toolName, args, result, success, ttl) {
        if (!this.db)
            throw new Error('Database not initialized');
        const id = this.generateId();
        const argsHash = this.hashObject(args);
        const now = new Date().toISOString();
        const expiresAt = ttl ? new Date(Date.now() + ttl).toISOString() : null;
        const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO tool_results_cache 
      (id, tool_name, arguments_hash, result, success, created_at, expires_at, access_count, last_accessed)
      VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?)
    `);
        stmt.run(id, toolName, argsHash, JSON.stringify(result), success ? 1 : 0, now, expiresAt, now);
    }
    async getCachedToolResult(toolName, args) {
        if (!this.db)
            throw new Error('Database not initialized');
        const argsHash = this.hashObject(args);
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
      SELECT result, success FROM tool_results_cache
      WHERE tool_name = ? AND arguments_hash = ?
      AND (expires_at IS NULL OR expires_at > ?)
    `);
        const row = stmt.get(toolName, argsHash, now);
        if (!row)
            return null;
        // Update access statistics
        const updateStmt = this.db.prepare(`
      UPDATE tool_results_cache 
      SET access_count = access_count + 1, last_accessed = ?
      WHERE tool_name = ? AND arguments_hash = ?
    `);
        updateStmt.run(now, toolName, argsHash);
        return {
            result: JSON.parse(row.result),
            success: row.success === 1,
        };
    }
    // Context Cache Management
    async cacheFileContent(filePath, content, metadata = {}) {
        if (!this.db)
            throw new Error('Database not initialized');
        const id = this.generateId();
        const contentHash = this.hashString(content);
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO context_cache 
      (id, path, content_hash, content, metadata, created_at, last_modified, access_count, last_accessed)
      VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?)
    `);
        stmt.run(id, filePath, contentHash, content, JSON.stringify(metadata), now, now, now);
    }
    async getCachedFileContent(filePath) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      SELECT content, metadata FROM context_cache
      WHERE path = ?
      ORDER BY last_modified DESC
      LIMIT 1
    `);
        const row = stmt.get(filePath);
        if (!row)
            return null;
        // Update access statistics
        const updateStmt = this.db.prepare(`
      UPDATE context_cache 
      SET access_count = access_count + 1, last_accessed = ?
      WHERE path = ?
    `);
        updateStmt.run(new Date().toISOString(), filePath);
        return {
            content: row.content,
            metadata: JSON.parse(row.metadata || '{}'),
        };
    }
    // Logging
    async log(level, message, metadata = {}, source) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT INTO logs (level, message, metadata, timestamp, source)
      VALUES (?, ?, ?, ?, ?)
    `);
        stmt.run(level, message, JSON.stringify(metadata), new Date().toISOString(), source);
    }
    async getLogs(options = {}) {
        if (!this.db)
            throw new Error('Database not initialized');
        const { level, source, limit = 100, offset = 0, since } = options;
        const conditions = [];
        const values = [];
        if (level) {
            conditions.push('level = ?');
            values.push(level);
        }
        if (source) {
            conditions.push('source = ?');
            values.push(source);
        }
        if (since) {
            conditions.push('timestamp >= ?');
            values.push(since.toISOString());
        }
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        values.push(limit, offset);
        const stmt = this.db.prepare(`
      SELECT * FROM logs 
      ${whereClause}
      ORDER BY timestamp DESC 
      LIMIT ? OFFSET ?
    `);
        const rows = stmt.all(...values);
        return rows.map((row) => ({
            ...row,
            metadata: JSON.parse(row.metadata || '{}'),
            timestamp: new Date(row.timestamp),
        }));
    }
    // Maintenance
    async cleanupExpiredCache() {
        if (!this.db)
            throw new Error('Database not initialized');
        const now = new Date().toISOString();
        // Clean expired tool results
        const toolStmt = this.db.prepare(`
      DELETE FROM tool_results_cache 
      WHERE expires_at IS NOT NULL AND expires_at < ?
    `);
        toolStmt.run(now);
        // Clean old logs (keep last 30 days)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
        const logStmt = this.db.prepare(`
      DELETE FROM logs WHERE timestamp < ?
    `);
        logStmt.run(thirtyDaysAgo);
    }
    async vacuum() {
        if (!this.db)
            throw new Error('Database not initialized');
        this.db.exec('VACUUM');
    }
    setupMaintenance() {
        // Clean up expired cache every hour
        setInterval(() => {
            this.cleanupExpiredCache().catch(console.error);
        }, 60 * 60 * 1000);
        // Vacuum database weekly
        setInterval(() => {
            this.vacuum().catch(console.error);
        }, index_1.DB_CONSTANTS.VACUUM_INTERVAL);
    }
    async close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
    // Utility methods
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString(36);
    }
    hashObject(obj) {
        return this.hashString(JSON.stringify(obj, Object.keys(obj).sort()));
    }
    flattenObject(obj, prefix, callback) {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const fullKey = prefix ? `${prefix}.${key}` : key;
                const value = obj[key];
                if (value && typeof value === 'object' && !Array.isArray(value)) {
                    this.flattenObject(value, fullKey, callback);
                }
                else {
                    callback(fullKey, value);
                }
            }
        }
    }
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        current[keys[keys.length - 1]] = value;
    }
}
exports.DatabaseService = DatabaseService;
//# sourceMappingURL=DatabaseService.js.map