{"version": 3, "file": "replace_in_file.js", "sourceRoot": "", "sources": ["../../../../src/main/tools/replace_in_file.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,0BA+HC;AAzKD,gDAAkC;AAClC,2CAA6B;AAC7B,mCAAoC;AAEpC,iDAAoD;AAEvC,QAAA,QAAQ,GAAG,2BAAe,CAAC,WAAW,CAAC;AAEvC,QAAA,MAAM,GAAe;IAChC,IAAI,EAAE,iBAAiB;IACvB,WAAW,EAAE,qKAAqK;IAClL,QAAQ,EAAE,2BAAe,CAAC,WAAW;IACrC,SAAS,EAAE,QAAQ;IACnB,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,uEAAuE;gBACpF,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,IAAI;aACf;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,uEAAuE;gBACpF,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;aACd;SACF;QACD,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC;KAC7C;CACF,CAAC;AAEK,KAAK,UAAU,OAAO,CAAC,IAK7B;IAaC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;IAEjE,IAAI,CAAC;QACH,qBAAqB;QACrB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5C,uBAAuB;QACvB,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,mBAAmB,QAAQ,EAAE;gBACtC,OAAO,EAAE;oBACP,QAAQ,EAAE,YAAY;oBACtB,WAAW,EAAE,EAAE;oBACf,WAAW,EAAE,EAAE;oBACf,YAAY,EAAE,CAAC;oBACf,IAAI,EAAE,EAAE;iBACT;gBACD,KAAK,EAAE,gBAAgB;aACxB,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEjE,uCAAuC;QACvC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,sCAAsC,QAAQ,EAAE;gBACzD,OAAO,EAAE;oBACP,QAAQ,EAAE,YAAY;oBACtB,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC5D,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC5D,YAAY,EAAE,CAAC;oBACf,IAAI,EAAE,wBAAwB;iBAC/B;gBACD,KAAK,EAAE,gBAAgB;aACxB,CAAC;QACJ,CAAC;QAED,mDAAmD;QACnD,MAAM,WAAW,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACjG,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB,WAAW,gFAAgF;gBAC7H,OAAO,EAAE;oBACP,QAAQ,EAAE,YAAY;oBACtB,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC5D,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC5D,YAAY,EAAE,CAAC;oBACf,IAAI,EAAE,SAAS,WAAW,cAAc;iBACzC;gBACD,KAAK,EAAE,wBAAwB;aAChC,CAAC;QACJ,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAA8B,CAAC;QACnC,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACjE,UAAU,GAAG,GAAG,YAAY,WAAW,SAAS,EAAE,CAAC;YACnD,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAC9C,CAAC;QAED,sBAAsB;QACtB,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7D,oBAAoB;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAC5C,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;QAEnD,gBAAgB;QAChB,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE5C,oBAAoB;QACpB,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAEtD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,iCAAiC,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,qBAAqB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1H,OAAO,EAAE;gBACP,QAAQ,EAAE,YAAY;gBACtB,UAAU;gBACV,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC5D,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC5D,YAAY;gBACZ,IAAI;aACL;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,mCAAmC,KAAK,CAAC,OAAO,EAAE;YAC3D,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAChC,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC5D,WAAW,EAAE,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC5D,YAAY,EAAE,CAAC;gBACf,IAAI,EAAE,EAAE;aACT;YACD,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,YAAY,CAAC,OAAe,EAAE,OAAe;IACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAErC,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAE5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5B,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACnD,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;gBACxB,IAAI,IAAI,KAAK,OAAO,OAAO,OAAO,IAAI,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC;QAC3B,CAAC;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}