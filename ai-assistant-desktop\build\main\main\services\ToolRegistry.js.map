{"version": 3, "file": "ToolRegistry.js", "sourceRoot": "", "sources": ["../../../../src/main/services/ToolRegistry.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA6B;AAC7B,+BAA4B;AAC5B,8CAAsB;AAYtB,MAAa,YAAY;IAKvB;QAJQ,UAAK,GAAgC,IAAI,GAAG,EAAE,CAAC;QAKrD,IAAI,CAAC,GAAG,GAAG,IAAI,aAAG,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,SAAS,GAAG,MAAM,IAAA,WAAI,EAAC,MAAM,EAAE;gBACnC,GAAG,EAAE,IAAI,CAAC,cAAc;gBACxB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YAEH,iBAAiB;YACjB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB;QACrC,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,UAAU,GAAG,yBAAa,QAAQ,uCAAC,CAAC;YAE1C,oEAAoE;YACpE,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YAED,MAAM,MAAM,GAAe,UAAU,CAAC,MAAM,CAAC;YAC7C,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACnC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,SAAS,CAAC;YAElD,4BAA4B;YAC5B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,cAAc,GAAmB;gBACrC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,QAAQ;gBACR,MAAM;gBACN,OAAO;aACR,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,IAAI,KAAK,QAAQ,GAAG,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,MAAkB;QAC3C,OAAO,CAAC,CAAC,CACP,MAAM,CAAC,IAAI;YACX,MAAM,CAAC,WAAW;YAClB,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CACjC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,IAAyB;QACvD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,QAAQ,EAAE,IAAI;gBACd,IAAI;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,OAAgB;gBACxB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS,IAAI,aAAa;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAC5B,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,QAAQ,EAAE,IAAI;gBACd,IAAI;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,OAAgB;gBACxB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,sBAAsB,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjE,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,QAAQ,EAAE,IAAI;gBACd,IAAI;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,MAAM;gBACd,MAAM;gBACN,MAAM,EAAE,SAAkB;gBAC1B,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ;gBACR,QAAQ,EAAE;oBACR,QAAQ;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,QAAQ,EAAE,IAAI;gBACd,IAAI;gBACJ,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,OAAgB;gBACxB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,uBAAuB;gBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,iBAAiB,CACvB,MAAkB,EAClB,IAAyB;QAEzB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,4BAA4B;QAC5B,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACnD,IAAI,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,+BAA+B,SAAS,EAAE,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACjC,KAAK,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjF,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;oBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC9B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;wBACjD,MAAM,CAAC,IAAI,CAAC,8BAA8B,SAAS,cAAc,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;oBACpF,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,KAAU,EAAE,KAAU;QAClD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YACnC,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YACnC,KAAK,SAAS;gBACZ,OAAO,OAAO,KAAK,KAAK,SAAS,CAAC;YACpC,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9E;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,kBAAkB,CAAC,QAAgB;QACjC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED,aAAa,CAAC,IAAY;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACnC,CAAC;IAED,cAAc;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClE,CAAC;IAED,oBAAoB;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,mCAAmC,CAAC;QAC7C,CAAC;QAED,IAAI,MAAM,GAAG,6CAA6C,CAAC;QAE3D,0BAA0B;QAC1B,MAAM,UAAU,GAAG,IAAI,GAAG,EAA4B,CAAC;QACvD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACpC,CAAC;YACD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,UAAU,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,QAAQ,EAAE,EAAE;YAC7C,MAAM,IAAI,MAAM,QAAQ,CAAC,WAAW,EAAE,YAAY,CAAC;YAEnD,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC3B,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC;gBAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,MAAM,CAAC;gBAEpC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnG,MAAM,IAAI,eAAe,CAAC;oBAC1B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE;wBAClF,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;wBACtG,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC3F,MAAM,IAAI,KAAK,SAAS,KAAK,QAAQ,CAAC,IAAI,IAAI,QAAQ,GAAG,UAAU,KAAK,QAAQ,CAAC,WAAW,IAAI,CAAC;oBACnG,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,2BAA2B,CAAC;gBACxC,CAAC;gBACD,MAAM,IAAI,IAAI,CAAC;YACjB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI;;;;;;;;;;;;;;;CAeb,CAAC;QAEE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,0BAA0B;IAC1B,YAAY,CAAC,cAA8B;QACzC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,2BAA2B,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,oBAAoB,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,cAAc,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,eAAe,CAAC,IAAY;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,iBAAiB;QACf,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED,yCAAyC;IACzC,qBAAqB,CAAC,YAAsB;QAC1C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACnD,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;IACJ,CAAC;CACF;AAzTD,oCAyTC"}