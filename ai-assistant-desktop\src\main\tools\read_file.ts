import * as fs from 'fs/promises';
import * as path from 'path';
import { lookup } from 'mime-types';
import type { ToolSchema } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

export const category = TOOL_CATEGORIES.FILE_SYSTEM;

export const schema: ToolSchema = {
  name: 'read_file',
  description: 'Read the contents of a file from the filesystem. Supports text files, with automatic encoding detection and binary file handling.',
  category: TOOL_CATEGORIES.FILE_SYSTEM,
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The absolute or relative path to the file to read',
      },
      encoding: {
        type: 'string',
        description: 'Text encoding to use (default: utf8). Use "binary" for binary files.',
        default: 'utf8',
      },
      maxSize: {
        type: 'number',
        description: 'Maximum file size to read in bytes (default: 10MB)',
        default: 10 * 1024 * 1024,
      },
    },
    required: ['path'],
  },
  examples: [
    {
      description: 'Read a text file',
      arguments: { path: './README.md' },
      expectedOutput: 'File contents as text',
    },
    {
      description: 'Read a binary file as base64',
      arguments: { path: './image.png', encoding: 'binary' },
      expectedOutput: 'Base64 encoded binary data',
    },
  ],
  riskLevel: 'low',
};

export async function execute(args: {
  path: string;
  encoding?: string;
  maxSize?: number;
}): Promise<{
  content: string;
  size: number;
  mimeType?: string;
  encoding: string;
  lastModified: string;
  absolutePath: string;
}> {
  const { path: filePath, encoding = 'utf8', maxSize = 10 * 1024 * 1024 } = args;
  
  try {
    // Resolve to absolute path
    const absolutePath = path.resolve(filePath);
    
    // Check if file exists and get stats
    const stats = await fs.stat(absolutePath);
    
    if (!stats.isFile()) {
      throw new Error(`Path is not a file: ${absolutePath}`);
    }
    
    if (stats.size > maxSize) {
      throw new Error(`File too large: ${stats.size} bytes (max: ${maxSize} bytes)`);
    }
    
    // Determine MIME type
    const mimeType = lookup(absolutePath) || 'application/octet-stream';
    
    // Read file content
    let content: string;
    if (encoding === 'binary' || mimeType.startsWith('image/') || mimeType.startsWith('application/')) {
      // For binary files, return base64 encoded content
      const buffer = await fs.readFile(absolutePath);
      content = buffer.toString('base64');
    } else {
      // For text files, use specified encoding
      content = await fs.readFile(absolutePath, { encoding: encoding as BufferEncoding });
    }
    
    return {
      content,
      size: stats.size,
      mimeType,
      encoding: encoding === 'binary' ? 'base64' : encoding,
      lastModified: stats.mtime.toISOString(),
      absolutePath,
    };
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      throw new Error(`File not found: ${filePath}`);
    } else if (error.code === 'EACCES') {
      throw new Error(`Permission denied: ${filePath}`);
    } else if (error.code === 'EISDIR') {
      throw new Error(`Path is a directory: ${filePath}`);
    } else {
      throw new Error(`Error reading file: ${error.message}`);
    }
  }
}

// Helper function to read file in chunks for large files
export async function readFileChunked(args: {
  path: string;
  chunkSize?: number;
  startOffset?: number;
  endOffset?: number;
}): Promise<{
  content: string;
  totalSize: number;
  chunkStart: number;
  chunkEnd: number;
  hasMore: boolean;
}> {
  const { path: filePath, chunkSize = 1024 * 1024, startOffset = 0, endOffset } = args;
  
  try {
    const absolutePath = path.resolve(filePath);
    const stats = await fs.stat(absolutePath);
    
    if (!stats.isFile()) {
      throw new Error(`Path is not a file: ${absolutePath}`);
    }
    
    const totalSize = stats.size;
    const actualEndOffset = endOffset !== undefined ? Math.min(endOffset, totalSize) : Math.min(startOffset + chunkSize, totalSize);
    
    // Open file and read chunk
    const fileHandle = await fs.open(absolutePath, 'r');
    const buffer = Buffer.alloc(actualEndOffset - startOffset);
    
    try {
      await fileHandle.read(buffer, 0, actualEndOffset - startOffset, startOffset);
      const content = buffer.toString('utf8');
      
      return {
        content,
        totalSize,
        chunkStart: startOffset,
        chunkEnd: actualEndOffset,
        hasMore: actualEndOffset < totalSize,
      };
    } finally {
      await fileHandle.close();
    }
  } catch (error: any) {
    throw new Error(`Error reading file chunk: ${error.message}`);
  }
}