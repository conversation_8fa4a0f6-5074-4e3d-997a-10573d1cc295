import type { ToolSchema } from '@shared/types';
export declare const category: "file_system";
export declare const schema: ToolSchema;
export declare function execute(args: {
    pattern: string;
    cwd?: string;
    includeDirectories?: boolean;
    followSymlinks?: boolean;
    maxResults?: number;
    ignorePatterns?: string[];
}): Promise<{
    success: boolean;
    message: string;
    results: {
        files: Array<{
            path: string;
            relativePath: string;
            isDirectory: boolean;
            size?: number;
            modified?: string;
        }>;
        totalFound: number;
        pattern: string;
        searchDirectory: string;
    };
    error?: string;
}>;
//# sourceMappingURL=glob.d.ts.map