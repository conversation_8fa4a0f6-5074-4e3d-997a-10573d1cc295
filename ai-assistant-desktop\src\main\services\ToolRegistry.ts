import * as path from 'path';
import { glob } from 'glob';
import Ajv from 'ajv';
import type { ToolSchema, ToolCall, ToolResult } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

export interface ToolDefinition {
  name: string;
  description: string;
  category: string;
  schema: ToolSchema;
  execute: (args: Record<string, any>) => Promise<any>;
}

export class ToolRegistry {
  private tools: Map<string, ToolDefinition> = new Map();
  private ajv: Ajv;
  private toolsDirectory: string;

  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    this.toolsDirectory = path.join(__dirname, '../tools');
  }

  async initialize(): Promise<void> {
    await this.loadTools();
    console.log(`Loaded ${this.tools.size} tools:`, Array.from(this.tools.keys()));
  }

  private async loadTools(): Promise<void> {
    try {
      // Find all tool files
      const toolFiles = await glob('*.ts', { 
        cwd: this.toolsDirectory,
        absolute: true 
      });

      // Load each tool
      for (const toolFile of toolFiles) {
        try {
          await this.loadTool(toolFile);
        } catch (error) {
          console.error(`Failed to load tool from ${toolFile}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to scan tools directory:', error);
    }
  }

  private async loadTool(toolPath: string): Promise<void> {
    try {
      // Import the tool module
      const toolModule = await import(toolPath);
      
      // Each tool should export: schema, execute, and optionally category
      if (!toolModule.schema || !toolModule.execute) {
        throw new Error('Tool must export schema and execute function');
      }

      const schema: ToolSchema = toolModule.schema;
      const execute = toolModule.execute;
      const category = toolModule.category || 'utility';

      // Validate schema structure
      if (!this.validateToolSchema(schema)) {
        throw new Error(`Invalid tool schema for ${schema.name}`);
      }

      const toolDefinition: ToolDefinition = {
        name: schema.name,
        description: schema.description,
        category,
        schema,
        execute,
      };

      this.tools.set(schema.name, toolDefinition);
      console.log(`Loaded tool: ${schema.name} (${category})`);
    } catch (error) {
      console.error(`Error loading tool from ${toolPath}:`, error);
      throw error;
    }
  }

  private validateToolSchema(schema: ToolSchema): boolean {
    return !!(
      schema.name &&
      schema.description &&
      Array.isArray(schema.parameters)
    );
  }

  async executeTool(name: string, args: Record<string, any>): Promise<ToolResult> {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        id: this.generateId(),
        toolName: name,
        name,
        input: args,
        output: null,
        result: null,
        status: 'error' as const,
        success: false,
        error: `Tool '${name}' not found`,
        timestamp: new Date(),
      };
    }

    // Validate arguments against schema
    const validationResult = this.validateArguments(tool.schema, args);
    if (!validationResult.valid) {
      return {
        id: this.generateId(),
        toolName: name,
        name,
        input: args,
        output: null,
        result: null,
        status: 'error' as const,
        success: false,
        error: `Invalid arguments: ${validationResult.errors.join(', ')}`,
        timestamp: new Date(),
      };
    }

    try {
      const startTime = Date.now();
      const result = await tool.execute(args);
      const duration = Date.now() - startTime;

      return {
        id: this.generateId(),
        toolName: name,
        name,
        input: args,
        output: result,
        result,
        status: 'success' as const,
        success: true,
        timestamp: new Date(),
        duration,
        metadata: {
          duration,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error: any) {
      return {
        id: this.generateId(),
        toolName: name,
        name,
        input: args,
        output: null,
        result: null,
        status: 'error' as const,
        success: false,
        error: error.message || 'Tool execution failed',
        timestamp: new Date(),
        metadata: {
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  private validateArguments(
    schema: ToolSchema, 
    args: Record<string, any>
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required parameters
    if (schema.parameters.required) {
      for (const paramName of schema.parameters.required) {
        if (!(paramName in args)) {
          errors.push(`Missing required parameter: ${paramName}`);
        }
      }
    }

    // Validate parameter types
    if (schema.parameters.properties) {
      for (const [paramName, paramDef] of Object.entries(schema.parameters.properties)) {
        if (paramName in args) {
          const value = args[paramName];
          if (!this.validateParameterType(paramDef, value)) {
            errors.push(`Invalid type for parameter ${paramName}: expected ${paramDef.type}`);
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private validateParameterType(param: any, value: any): boolean {
    switch (param.type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  getAvailableTools(): ToolDefinition[] {
    return Array.from(this.tools.values());
  }

  getToolsByCategory(category: string): ToolDefinition[] {
    return Array.from(this.tools.values()).filter(tool => tool.category === category);
  }

  getToolSchema(name: string): ToolSchema | null {
    const tool = this.tools.get(name);
    return tool ? tool.schema : null;
  }

  getToolSchemas(): ToolSchema[] {
    return Array.from(this.tools.values()).map(tool => tool.schema);
  }

  generateSystemPrompt(): string {
    const tools = this.getAvailableTools();
    if (tools.length === 0) {
      return 'No tools are currently available.';
    }

    let prompt = 'You have access to the following tools:\n\n';

    // Group tools by category
    const categories = new Map<string, ToolDefinition[]>();
    tools.forEach(tool => {
      if (!categories.has(tool.category)) {
        categories.set(tool.category, []);
      }
      categories.get(tool.category)!.push(tool);
    });

    // Generate prompt for each category
    categories.forEach((categoryTools, category) => {
      prompt += `## ${category.toUpperCase()} TOOLS\n\n`;
      
      categoryTools.forEach(tool => {
        prompt += `### ${tool.name}\n`;
        prompt += `${tool.description}\n\n`;
        
        if (tool.schema.parameters.properties && Object.keys(tool.schema.parameters.properties).length > 0) {
          prompt += 'Parameters:\n';
          Object.entries(tool.schema.parameters.properties).forEach(([paramName, paramDef]) => {
            const required = tool.schema.parameters.required?.includes(paramName) ? ' (required)' : ' (optional)';
            const defaultVal = paramDef.default !== undefined ? ` [default: ${paramDef.default}]` : '';
            prompt += `- ${paramName} (${paramDef.type})${required}${defaultVal}: ${paramDef.description}\n`;
          });
        } else {
          prompt += 'No parameters required.\n';
        }
        prompt += '\n';
      });
    });

    prompt += `
When using tools, always:
1. Provide all required parameters
2. Use appropriate parameter types
3. Handle tool results appropriately
4. Explain what you're doing before executing tools

Tools should be called as JSON objects with the following structure:
{
  "name": "tool_name",
  "arguments": {
    "param1": "value1",
    "param2": "value2"
  }
}
`;

    return prompt;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Tool management methods
  registerTool(toolDefinition: ToolDefinition): void {
    if (!this.validateToolSchema(toolDefinition.schema)) {
      throw new Error(`Invalid tool schema for ${toolDefinition.name}`);
    }

    this.tools.set(toolDefinition.name, toolDefinition);
    console.log(`Registered tool: ${toolDefinition.name}`);
  }

  unregisterTool(name: string): boolean {
    return this.tools.delete(name);
  }

  isToolAvailable(name: string): boolean {
    return this.tools.has(name);
  }

  getToolCategories(): string[] {
    const categories = new Set<string>();
    this.tools.forEach(tool => categories.add(tool.category));
    return Array.from(categories);
  }

  // Enable/disable tools based on settings
  filterToolsBySettings(enabledTools: string[]): ToolDefinition[] {
    return Array.from(this.tools.values()).filter(tool => 
      enabledTools.includes(tool.name)
    );
  }
}