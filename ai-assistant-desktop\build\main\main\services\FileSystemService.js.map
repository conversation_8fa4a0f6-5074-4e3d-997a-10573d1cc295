{"version": 3, "file": "FileSystemService.js", "sourceRoot": "", "sources": ["../../../../src/main/services/FileSystemService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,2CAA6B;AAC7B,+BAA4B;AAG5B,MAAa,iBAAiB;IAC5B,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC7B,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACzD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,OAAe;QAC/C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE7C,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAe;QACjC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAExE,MAAM,KAAK,GAAe,EAAE,CAAC;YAE7B,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBACrD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEtC,MAAM,QAAQ,GAAa;oBACzB,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;oBAChD,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;oBAC7C,QAAQ,EAAE,KAAK,CAAC,KAAK;oBACrB,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;iBAC/E,CAAC;gBAEF,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,wBAAwB,OAAO,EAAE,CAAC,CAAC;YACrD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAe,EAAE,OAIhC;QACC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;YAElF,IAAI,OAAO,GAAG,MAAM,IAAA,WAAI,EAAC,OAAO,EAAE;gBAChC,GAAG;gBACH,QAAQ;aACT,CAAC,CAAC;YAEH,kCAAkC;YAClC,IAAI,UAAU,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;gBAC9C,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,QAAgB;QAC3B,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAO7B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACpD,OAAO;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;gBACtB,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE;gBAChC,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrB,OAAO,EAAE,KAAK,CAAC,SAAS;aACzB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,SAAS,GAAG,IAAI;QACrD,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAc,EAAE,WAAmB;QAChD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAEtD,sCAAsC;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE7C,MAAM,EAAE,CAAC,QAAQ,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAc,EAAE,WAAmB;QAChD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAEtD,sCAAsC;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAClD,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE7C,MAAM,EAAE,CAAC,MAAM,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,UAAkB,EAAE,OAAgB,EAAE,OAAgB;QACzF,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAExC,uEAAuE;QACvE,MAAM,KAAK,GAAe,EAAE,CAAC;QAC7B,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,OAAO,QAAQ,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;YAChE,MAAM,YAAY,GAAG,QAAQ,CAAC;YAC9B,MAAM,YAAY,GAAG,QAAQ,CAAC;YAC9B,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,mBAAmB;YACnB,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,cAAc,GAAG,EAAE,EAAE,CAAC;gBACzF,MAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC5E,MAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAE5E,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;oBACxB,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACxC,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,OAAO,IAAI,EAAE;4BACtB,SAAS,EAAE,QAAQ,GAAG,CAAC;4BACvB,SAAS,EAAE,QAAQ,GAAG,CAAC;yBACxB,CAAC,CAAC;oBACL,CAAC;oBACD,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,CAAC;gBACb,CAAC;qBAAM,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;oBAC/B,eAAe;oBACf,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,OAAO;wBAChB,SAAS,EAAE,QAAQ,GAAG,CAAC;qBACxB,CAAC,CAAC;oBACH,QAAQ,EAAE,CAAC;oBACX,SAAS,GAAG,IAAI,CAAC;gBACnB,CAAC;qBAAM,IAAI,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;oBAC/B,aAAa;oBACb,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,KAAK;wBACX,OAAO,EAAE,OAAO;wBAChB,SAAS,EAAE,QAAQ,GAAG,CAAC;qBACxB,CAAC,CAAC;oBACH,QAAQ,EAAE,CAAC;oBACX,SAAS,GAAG,IAAI,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACN,uCAAuC;oBACvC,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,OAAQ;wBACjB,SAAS,EAAE,QAAQ,GAAG,CAAC;qBACxB,CAAC,CAAC;oBACH,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,KAAK;wBACX,OAAO,EAAE,OAAQ;wBACjB,SAAS,EAAE,QAAQ,GAAG,CAAC;qBACxB,CAAC,CAAC;oBACH,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,CAAC;oBACX,SAAS,GAAG,IAAI,CAAC;gBACnB,CAAC;gBAED,cAAc,EAAE,CAAC;gBAEjB,qDAAqD;gBACrD,IAAI,SAAS,IAAI,OAAO,KAAK,OAAO,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;oBAC3D,MAAM;gBACR,CAAC;YACH,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,GAAa;oBACrB,QAAQ,EAAE,YAAY,GAAG,CAAC;oBAC1B,QAAQ,EAAE,QAAQ,GAAG,YAAY;oBACjC,QAAQ,EAAE,YAAY,GAAG,CAAC;oBAC1B,QAAQ,EAAE,QAAQ,GAAG,YAAY;oBACjC,KAAK,EAAE,SAAS;iBACjB,CAAC;gBAEF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO,IAAI,KAAK;YACzB,OAAO,EAAE,OAAO,IAAI,KAAK;YACzB,KAAK;SACN,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,UAAkB,EAAE,WAAmB,EAAE,OAK9E;QAMC,MAAM,EACJ,KAAK,GAAG,KAAK,EACb,MAAM,GAAG,IAAI,EACb,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,IAAI,GACpB,GAAG,OAAO,IAAI,EAAE,CAAC;QAElB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE1D,IAAI,UAAkB,CAAC;YACvB,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAChE,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBACnD,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACpD,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,0BAA0B;gBAC1B,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;oBAC5E,MAAM,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;oBAExF,IAAI,UAAU,GAAG,CAAC,CAAC;oBACnB,IAAI,WAAW,GAAG,eAAe,CAAC;oBAElC,OAAO,IAAI,EAAE,CAAC;wBACZ,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;wBACjE,IAAI,KAAK,KAAK,CAAC,CAAC;4BAAE,MAAM;wBAExB,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;4BAChC,WAAW;4BACX,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;wBAC9D,UAAU,GAAG,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;wBACxC,YAAY,EAAE,CAAC;oBACjB,CAAC;oBAED,UAAU,GAAG,WAAW,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACN,qBAAqB;oBACrB,MAAM,KAAK,GAAG,aAAa;wBACzB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC;wBACrC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;oBAEpE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBACjB,UAAU,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;4BACpC,WAAW;4BACX,eAAe,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;wBACjE,YAAY,GAAG,CAAC,CAAC;oBACnB,CAAC;yBAAM,CAAC;wBACN,UAAU,GAAG,eAAe,CAAC;oBAC/B,CAAC;gBACH,CAAC;YACH,CAAC;YAED,6BAA6B;YAC7B,IAAI,UAA8B,CAAC;YACnC,IAAI,YAAY,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrC,UAAU,GAAG,GAAG,YAAY,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;gBACpD,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAC9C,CAAC;YAED,wBAAwB;YACxB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEvF,oBAAoB;YACpB,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACjD,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,YAAY;gBACZ,OAAO;gBACP,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,4CAA4C;IAC5C,SAAS,CAAC,QAAgB,EAAE,QAAwD;QAClF,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QACjF,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;AA5WD,8CA4WC"}