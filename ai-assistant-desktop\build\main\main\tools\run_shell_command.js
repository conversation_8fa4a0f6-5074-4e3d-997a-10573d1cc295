"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
exports.executeStreaming = executeStreaming;
const child_process_1 = require("child_process");
const util_1 = require("util");
const constants_1 = require("@shared/constants");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
exports.category = constants_1.TOOL_CATEGORIES.SYSTEM;
exports.schema = {
    name: 'run_shell_command',
    description: 'Execute a shell command and return its output. Use this for system operations, running scripts, or interacting with command-line tools.',
    category: constants_1.TOOL_CATEGORIES.SYSTEM,
    parameters: {
        type: 'object',
        properties: {
            command: {
                type: 'string',
                description: 'The shell command to execute',
            },
            workingDirectory: {
                type: 'string',
                description: 'Working directory for the command (optional)',
            },
            timeout: {
                type: 'number',
                description: 'Command timeout in milliseconds (default: 30000)',
                default: 30000,
            },
            shell: {
                type: 'string',
                description: 'Shell to use for command execution (defaults to system shell)',
            },
        },
        required: ['command'],
    },
    examples: [
        {
            description: 'List files in current directory',
            arguments: { command: 'ls -la' },
            expectedOutput: 'Directory listing with file details',
        },
        {
            description: 'Check Node.js version',
            arguments: { command: 'node --version' },
            expectedOutput: 'Node.js version number',
        },
    ],
    riskLevel: 'high',
    requiresConfirmation: true,
};
async function execute(args) {
    const { command, workingDirectory, timeout = 30000, shell } = args;
    const startTime = Date.now();
    try {
        const options = {
            cwd: workingDirectory || process.cwd(),
            timeout,
            killSignal: 'SIGTERM',
            maxBuffer: 1024 * 1024 * 10, // 10MB buffer
        };
        if (shell) {
            options.shell = shell;
        }
        const { stdout, stderr } = await execAsync(command, options);
        return {
            stdout: stdout.toString(),
            stderr: stderr.toString(),
            exitCode: 0,
            command,
            executionTime: Date.now() - startTime,
        };
    }
    catch (error) {
        return {
            stdout: error.stdout?.toString() || '',
            stderr: error.stderr?.toString() || error.message,
            exitCode: error.code || 1,
            command,
            executionTime: Date.now() - startTime,
        };
    }
}
// Enhanced version with streaming support
async function executeStreaming(args) {
    const { command, workingDirectory, shell, onStdout, onStderr } = args;
    const startTime = Date.now();
    return new Promise((resolve, reject) => {
        const options = {
            cwd: workingDirectory || process.cwd(),
            shell: shell || true,
        };
        const child = (0, child_process_1.spawn)(command, [], options);
        let exitCode = 0;
        child.stdout?.on('data', (data) => {
            if (onStdout) {
                onStdout(data.toString());
            }
        });
        child.stderr?.on('data', (data) => {
            if (onStderr) {
                onStderr(data.toString());
            }
        });
        child.on('error', (error) => {
            reject(error);
        });
        child.on('close', (code) => {
            exitCode = code || 0;
            resolve({
                exitCode,
                command,
                executionTime: Date.now() - startTime,
            });
        });
        // Set a reasonable timeout for streaming commands
        setTimeout(() => {
            if (!child.killed) {
                child.kill('SIGTERM');
                reject(new Error(`Command timed out: ${command}`));
            }
        }, 60000); // 60 seconds
    });
}
//# sourceMappingURL=run_shell_command.js.map