"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
exports.getDirectoryTree = getDirectoryTree;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const constants_1 = require("@shared/constants");
exports.category = constants_1.TOOL_CATEGORIES.FILE_SYSTEM;
exports.schema = {
    name: 'list_directory',
    description: 'List the contents of a directory, showing files and subdirectories with their properties.',
    category: constants_1.TOOL_CATEGORIES.FILE_SYSTEM,
    riskLevel: 'low',
    parameters: {
        type: 'object',
        properties: {
            path: {
                type: 'string',
                description: 'The absolute or relative path to the directory to list',
                required: true,
            },
            recursive: {
                type: 'boolean',
                description: 'List contents recursively (default: false)',
                required: false,
                default: false,
            },
            includeHidden: {
                type: 'boolean',
                description: 'Include hidden files and directories (default: false)',
                required: false,
                default: false,
            },
            maxDepth: {
                type: 'number',
                description: 'Maximum depth for recursive listing (default: 5)',
                required: false,
                default: 5,
            },
            sortBy: {
                type: 'string',
                description: 'Sort results by: name, size, modified, type (default: name)',
                required: false,
                default: 'name',
                enum: ['name', 'size', 'modified', 'type'],
            },
        },
        required: ['path'],
    },
};
async function execute(args) {
    const { path: dirPath, recursive = false, includeHidden = false, maxDepth = 5, sortBy = 'name' } = args;
    try {
        const absolutePath = path.resolve(dirPath);
        // Check if path exists and is a directory
        const stats = await fs.stat(absolutePath);
        if (!stats.isDirectory()) {
            throw new Error(`Path is not a directory: ${absolutePath}`);
        }
        const allFiles = [];
        const allDirectories = [];
        await scanDirectory(absolutePath, allFiles, allDirectories, {
            recursive,
            includeHidden,
            maxDepth,
            currentDepth: 0,
        });
        // Sort results
        const sortFunction = createSortFunction(sortBy);
        allFiles.sort(sortFunction);
        allDirectories.sort(sortFunction);
        // Calculate total size
        const totalSize = allFiles.reduce((sum, file) => sum + (file.size || 0), 0);
        return {
            path: dirPath,
            absolutePath,
            files: allFiles,
            directories: allDirectories,
            totalFiles: allFiles.length,
            totalDirectories: allDirectories.length,
            totalSize,
        };
    }
    catch (error) {
        if (error.code === 'ENOENT') {
            throw new Error(`Directory not found: ${dirPath}`);
        }
        else if (error.code === 'EACCES') {
            throw new Error(`Permission denied: ${dirPath}`);
        }
        else if (error.code === 'ENOTDIR') {
            throw new Error(`Path is not a directory: ${dirPath}`);
        }
        else {
            throw new Error(`Error listing directory: ${error.message}`);
        }
    }
}
async function scanDirectory(dirPath, files, directories, options) {
    const { recursive, includeHidden, maxDepth, currentDepth } = options;
    try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        for (const entry of entries) {
            // Skip hidden files if not requested
            if (!includeHidden && entry.name.startsWith('.')) {
                continue;
            }
            const fullPath = path.join(dirPath, entry.name);
            try {
                const stats = await fs.stat(fullPath);
                const fileInfo = {
                    path: fullPath,
                    name: entry.name,
                    type: entry.isDirectory() ? 'directory' : 'file',
                    size: entry.isFile() ? stats.size : undefined,
                    modified: stats.mtime,
                    extension: entry.isFile() ? path.extname(entry.name).toLowerCase() : undefined,
                };
                if (entry.isDirectory()) {
                    directories.push(fileInfo);
                    // Recurse into subdirectories if requested
                    if (recursive && currentDepth < maxDepth) {
                        await scanDirectory(fullPath, files, directories, {
                            ...options,
                            currentDepth: currentDepth + 1,
                        });
                    }
                }
                else {
                    files.push(fileInfo);
                }
            }
            catch (entryError) {
                // Skip entries that can't be accessed
                console.warn(`Skipping inaccessible entry: ${fullPath}`, entryError);
            }
        }
    }
    catch (error) {
        console.warn(`Error scanning directory: ${dirPath}`, error);
    }
}
function createSortFunction(sortBy) {
    switch (sortBy) {
        case 'size':
            return (a, b) => (b.size || 0) - (a.size || 0);
        case 'modified':
            return (a, b) => {
                const aTime = a.modified ? new Date(a.modified).getTime() : 0;
                const bTime = b.modified ? new Date(b.modified).getTime() : 0;
                return bTime - aTime;
            };
        case 'type':
            return (a, b) => {
                // Directories first, then by extension
                if (a.type !== b.type) {
                    return a.type === 'directory' ? -1 : 1;
                }
                if (a.extension && b.extension) {
                    return a.extension.localeCompare(b.extension);
                }
                return a.name.localeCompare(b.name);
            };
        case 'name':
        default:
            return (a, b) => a.name.localeCompare(b.name);
    }
}
// Helper function for getting directory tree structure
async function getDirectoryTree(args) {
    const { path: dirPath, maxDepth = 3, includeHidden = false } = args;
    async function buildTree(currentPath, depth) {
        const stats = await fs.stat(currentPath);
        const name = path.basename(currentPath);
        const node = {
            name,
            path: currentPath,
            type: stats.isDirectory() ? 'directory' : 'file',
            modified: stats.mtime,
        };
        if (stats.isFile()) {
            node.size = stats.size;
            node.extension = path.extname(currentPath).toLowerCase();
        }
        else if (stats.isDirectory() && depth < maxDepth) {
            try {
                const entries = await fs.readdir(currentPath, { withFileTypes: true });
                node.children = [];
                for (const entry of entries) {
                    if (!includeHidden && entry.name.startsWith('.')) {
                        continue;
                    }
                    const childPath = path.join(currentPath, entry.name);
                    try {
                        const child = await buildTree(childPath, depth + 1);
                        node.children.push(child);
                    }
                    catch (error) {
                        // Skip inaccessible children
                        console.warn(`Skipping inaccessible path: ${childPath}`);
                    }
                }
                // Sort children
                node.children.sort((a, b) => {
                    if (a.type !== b.type) {
                        return a.type === 'directory' ? -1 : 1;
                    }
                    return a.name.localeCompare(b.name);
                });
            }
            catch (error) {
                // Directory not readable
                console.warn(`Cannot read directory: ${currentPath}`);
            }
        }
        return node;
    }
    try {
        const absolutePath = path.resolve(dirPath);
        return await buildTree(absolutePath, 0);
    }
    catch (error) {
        throw new Error(`Error building directory tree: ${error.message}`);
    }
}
//# sourceMappingURL=list_directory.js.map