"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemService = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const os = __importStar(require("os"));
const process = __importStar(require("process"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class SystemService {
    async getSystemInfo() {
        return {
            platform: os.platform(),
            arch: os.arch(),
            nodeVersion: process.version,
            electronVersion: process.versions.electron || 'unknown',
            appVersion: process.env.npm_package_version || '1.0.0',
        };
    }
    async executeCommand(command, options) {
        const { cwd, timeout = 30000, shell = true, env } = options || {};
        const startTime = Date.now();
        try {
            const execOptions = {
                cwd: cwd || process.cwd(),
                timeout,
                shell,
                env: { ...process.env, ...env },
                maxBuffer: 1024 * 1024 * 10, // 10MB
            };
            const { stdout, stderr } = await execAsync(command, execOptions);
            return {
                stdout: stdout.toString(),
                stderr: stderr.toString(),
                exitCode: 0,
                command,
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                stdout: error.stdout?.toString() || '',
                stderr: error.stderr?.toString() || error.message,
                exitCode: error.code || 1,
                command,
                executionTime: Date.now() - startTime,
            };
        }
    }
    async executeCommandStreaming(command, options) {
        const { cwd, shell = true, env, onStdout, onStderr, onExit } = options || {};
        const startTime = Date.now();
        return new Promise((resolve, reject) => {
            const child = (0, child_process_1.spawn)(command, [], {
                cwd: cwd || process.cwd(),
                shell,
                env: { ...process.env, ...env },
                stdio: ['ignore', 'pipe', 'pipe'],
            });
            if (child.stdout) {
                child.stdout.on('data', (data) => {
                    if (onStdout) {
                        onStdout(data.toString());
                    }
                });
            }
            if (child.stderr) {
                child.stderr.on('data', (data) => {
                    if (onStderr) {
                        onStderr(data.toString());
                    }
                });
            }
            child.on('error', (error) => {
                reject(new Error(`Command failed to start: ${error.message}`));
            });
            child.on('close', (code) => {
                const exitCode = code || 0;
                if (onExit) {
                    onExit(exitCode);
                }
                resolve({
                    exitCode,
                    command,
                    executionTime: Date.now() - startTime,
                });
            });
            // Set timeout
            const timeout = setTimeout(() => {
                child.kill('SIGTERM');
                reject(new Error(`Command timed out: ${command}`));
            }, 60000);
            child.on('close', () => {
                clearTimeout(timeout);
            });
        });
    }
    getEnvironmentVariables() {
        return { ...process.env };
    }
    setEnvironmentVariable(key, value) {
        process.env[key] = value;
    }
    getCurrentWorkingDirectory() {
        return process.cwd();
    }
    changeWorkingDirectory(path) {
        process.chdir(path);
    }
    getProcessInfo() {
        return {
            pid: process.pid,
            ppid: process.ppid || 0,
            platform: process.platform,
            arch: process.arch,
            version: process.version,
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime(),
        };
    }
    async getSystemResources() {
        const cpus = os.cpus();
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        return {
            cpus,
            memory: {
                total: totalMemory,
                free: freeMemory,
                used: usedMemory,
                usage: (usedMemory / totalMemory) * 100,
            },
            load: os.loadavg(),
            uptime: os.uptime(),
        };
    }
    async getNetworkInterfaces() {
        return os.networkInterfaces();
    }
    async killProcess(pid, signal = 'SIGTERM') {
        try {
            process.kill(pid, signal);
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async listProcesses() {
        try {
            let command;
            if (process.platform === 'win32') {
                command = 'wmic process get ProcessId,Name,PageFileUsage,WorkingSetSize /format:csv';
            }
            else if (process.platform === 'darwin') {
                command = 'ps -axo pid,comm,%cpu,%mem';
            }
            else {
                // Linux and others
                command = 'ps -axo pid,comm,%cpu,%mem';
            }
            const result = await this.executeCommand(command);
            if (result.exitCode !== 0) {
                throw new Error(`Failed to list processes: ${result.stderr}`);
            }
            const lines = result.stdout.trim().split('\n');
            const processes = [];
            if (process.platform === 'win32') {
                // Parse Windows CSV output
                for (let i = 1; i < lines.length; i++) {
                    const parts = lines[i].split(',');
                    if (parts.length >= 4) {
                        const pid = parseInt(parts[3], 10);
                        const name = parts[1];
                        if (!isNaN(pid) && name) {
                            processes.push({ pid, name });
                        }
                    }
                }
            }
            else {
                // Parse Unix-style ps output
                for (let i = 1; i < lines.length; i++) {
                    const parts = lines[i].trim().split(/\s+/);
                    if (parts.length >= 4) {
                        const pid = parseInt(parts[0], 10);
                        const name = parts[1];
                        const cpu = parseFloat(parts[2]);
                        const memory = parseFloat(parts[3]);
                        if (!isNaN(pid) && name) {
                            processes.push({
                                pid,
                                name,
                                cpu: !isNaN(cpu) ? cpu : undefined,
                                memory: !isNaN(memory) ? memory : undefined,
                            });
                        }
                    }
                }
            }
            return processes;
        }
        catch (error) {
            throw new Error(`Error listing processes: ${error.message}`);
        }
    }
    async getSystemPath() {
        const pathVar = process.env.PATH || process.env.Path || '';
        const separator = process.platform === 'win32' ? ';' : ':';
        return pathVar.split(separator).filter(Boolean);
    }
    async findExecutable(name) {
        try {
            const command = process.platform === 'win32' ? `where ${name}` : `which ${name}`;
            const result = await this.executeCommand(command);
            if (result.exitCode === 0) {
                return result.stdout.trim().split('\n')[0];
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    async checkPortOpen(port, host = 'localhost') {
        return new Promise((resolve) => {
            const net = require('net');
            const socket = new net.Socket();
            socket.setTimeout(1000);
            socket.on('connect', () => {
                socket.destroy();
                resolve(true);
            });
            socket.on('timeout', () => {
                socket.destroy();
                resolve(false);
            });
            socket.on('error', () => {
                resolve(false);
            });
            socket.connect(port, host);
        });
    }
    async getUserInfo() {
        const userInfo = os.userInfo();
        return {
            username: userInfo.username,
            homedir: userInfo.homedir,
            shell: userInfo.shell,
        };
    }
    async getDiskUsage(path = '/') {
        try {
            let command;
            if (process.platform === 'win32') {
                // For Windows, get disk usage of the drive containing the path
                command = `powershell "Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq '${path.charAt(0).toUpperCase()}:'} | Select-Object Size,FreeSpace"`;
            }
            else {
                // For Unix-like systems
                command = `df -k "${path}"`;
            }
            const result = await this.executeCommand(command);
            if (result.exitCode !== 0) {
                return null;
            }
            if (process.platform === 'win32') {
                // Parse PowerShell output
                const lines = result.stdout.trim().split('\n');
                if (lines.length >= 3) {
                    const sizeLine = lines.find(line => line.includes('Size'));
                    const freeLine = lines.find(line => line.includes('FreeSpace'));
                    if (sizeLine && freeLine) {
                        const size = parseInt(sizeLine.split(':')[1].trim(), 10);
                        const free = parseInt(freeLine.split(':')[1].trim(), 10);
                        const used = size - free;
                        return {
                            total: size,
                            free: free,
                            used: used,
                            usage: (used / size) * 100,
                        };
                    }
                }
            }
            else {
                // Parse df output
                const lines = result.stdout.trim().split('\n');
                if (lines.length >= 2) {
                    const dataLine = lines[1].trim().split(/\s+/);
                    if (dataLine.length >= 4) {
                        const total = parseInt(dataLine[1], 10) * 1024; // Convert from KB to bytes
                        const used = parseInt(dataLine[2], 10) * 1024;
                        const free = parseInt(dataLine[3], 10) * 1024;
                        return {
                            total,
                            free,
                            used,
                            usage: (used / total) * 100,
                        };
                    }
                }
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
}
exports.SystemService = SystemService;
//# sourceMappingURL=SystemService.js.map