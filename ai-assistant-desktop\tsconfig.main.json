{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./build/main", "rootDir": "./src", "noEmit": false, "jsx": "preserve", "skipLibCheck": true, "noImplicitAny": false, "strictNullChecks": false}, "include": ["src/main/**/*", "src/shared/**/*"], "exclude": ["src/renderer", "src/**/__tests__/**/*", "src/**/*.test.ts", "src/**/*.spec.ts", "node_modules", "build", "dist"]}