"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const AgentService_1 = require("../AgentService");
const LLMService_1 = require("../LLMService");
const ToolRegistry_1 = require("../ToolRegistry");
// Mock dependencies
jest.mock('../LLMService');
jest.mock('../ToolRegistry');
const mockLLMService = LLMService_1.LLMService;
const mockToolRegistry = ToolRegistry_1.ToolRegistry;
describe('AgentService', () => {
    let agentService;
    let mockLLMInstance;
    let mockToolRegistryInstance;
    beforeEach(() => {
        mockLLMInstance = {
            generateResponse: jest.fn(),
            streamResponse: jest.fn(),
        };
        mockToolRegistryInstance = {
            executeTool: jest.fn(),
            getAvailableTools: jest.fn(),
            validateToolCall: jest.fn(),
        };
        mockLLMService.mockImplementation(() => mockLLMInstance);
        mockToolRegistry.mockImplementation(() => mockToolRegistryInstance);
        agentService = new AgentService_1.AgentService();
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('processMessage', () => {
        it('should process a simple message without tools', async () => {
            const mockResponse = {
                content: 'Hello! How can I help you?',
                usage: { promptTokens: 10, completionTokens: 8, totalTokens: 18 },
                finishReason: 'stop',
            };
            mockLLMInstance.generateResponse.mockResolvedValue(mockResponse);
            const result = await agentService.processMessage('Hello');
            expect(result.success).toBe(true);
            expect(result.response).toBe('Hello! How can I help you?');
            expect(mockLLMInstance.generateResponse).toHaveBeenCalledWith(expect.arrayContaining([
                expect.objectContaining({
                    role: 'user',
                    content: 'Hello',
                }),
            ]), expect.any(Object));
        });
        it('should handle LLM service errors', async () => {
            const error = new Error('API rate limit exceeded');
            mockLLMInstance.generateResponse.mockRejectedValue(error);
            const result = await agentService.processMessage('Hello');
            expect(result.success).toBe(false);
            expect(result.error).toBe('API rate limit exceeded');
        });
    });
    describe('createPlan', () => {
        it('should create a valid plan from user request', async () => {
            const mockPlanResponse = {
                content: JSON.stringify({
                    title: 'Create a new file',
                    description: 'Create a new JavaScript file with basic content',
                    requires_confirmation: true,
                    steps: [
                        {
                            id: 'step-1',
                            title: 'Create file',
                            description: 'Create new file with content',
                            tool: 'write_file',
                            parameters: {
                                path: 'test.js',
                                content: 'console.log("Hello World");',
                            },
                        },
                    ],
                }),
                usage: { promptTokens: 50, completionTokens: 100, totalTokens: 150 },
                finishReason: 'stop',
            };
            mockLLMInstance.generateResponse.mockResolvedValue(mockPlanResponse);
            const result = await agentService.createPlan('Create a new JavaScript file');
            expect(result.success).toBe(true);
            expect(result.plan).toBeDefined();
            expect(result.plan?.title).toBe('Create a new file');
            expect(result.plan?.steps).toHaveLength(1);
            expect(result.plan?.requires_confirmation).toBe(true);
        });
        it('should handle invalid plan JSON', async () => {
            const mockPlanResponse = {
                content: 'Invalid JSON response',
                usage: { promptTokens: 50, completionTokens: 10, totalTokens: 60 },
                finishReason: 'stop',
            };
            mockLLMInstance.generateResponse.mockResolvedValue(mockPlanResponse);
            const result = await agentService.createPlan('Create a file');
            expect(result.success).toBe(false);
            expect(result.error).toContain('Failed to parse plan');
        });
    });
    describe('confirmPlan', () => {
        beforeEach(() => {
            // Set up a mock plan
            const mockPlan = {
                id: 'plan-1',
                title: 'Test Plan',
                description: 'A test plan',
                requires_confirmation: true,
                steps: [
                    {
                        id: 'step-1',
                        title: 'Test Step',
                        description: 'A test step',
                        tool: 'write_file',
                        parameters: { path: 'test.txt', content: 'test' },
                    },
                ],
            };
            agentService.currentPlan = mockPlan;
            agentService.currentState = { isWaitingForConfirmation: true };
        });
        it('should confirm and prepare plan for execution', async () => {
            const result = await agentService.confirmPlan(true);
            expect(result.success).toBe(true);
            expect(result.message).toBe('Plan confirmed and ready for execution');
            expect(agentService.currentState.isWaitingForConfirmation).toBe(false);
        });
        it('should reject plan when not approved', async () => {
            const result = await agentService.confirmPlan(false);
            expect(result.success).toBe(true);
            expect(result.message).toBe('Execution cancelled by user');
            expect(agentService.currentPlan).toBeUndefined();
        });
        it('should handle modified plan', async () => {
            const modifiedPlan = {
                id: 'plan-1',
                title: 'Modified Test Plan',
                description: 'A modified test plan',
                requires_confirmation: true,
                steps: [
                    {
                        id: 'step-1',
                        title: 'Modified Test Step',
                        description: 'A modified test step',
                        tool: 'write_file',
                        parameters: { path: 'modified.txt', content: 'modified' },
                    },
                ],
            };
            const result = await agentService.confirmPlan(true, modifiedPlan);
            expect(result.success).toBe(true);
            expect(agentService.currentPlan.title).toBe('Modified Test Plan');
        });
    });
    describe('executePlan', () => {
        beforeEach(() => {
            const mockPlan = {
                id: 'plan-1',
                title: 'Test Plan',
                description: 'A test plan',
                requires_confirmation: false,
                steps: [
                    {
                        id: 'step-1',
                        title: 'Test Step',
                        description: 'A test step',
                        tool: 'write_file',
                        parameters: { path: 'test.txt', content: 'test' },
                    },
                ],
            };
            agentService.currentPlan = mockPlan;
            agentService.currentState = { isWaitingForConfirmation: false };
        });
        it('should execute plan successfully', async () => {
            const mockToolResult = {
                success: true,
                output: 'File created successfully',
                data: { path: 'test.txt' },
            };
            mockToolRegistryInstance.executeTool.mockResolvedValue(mockToolResult);
            const result = await agentService.executePlan();
            expect(result.success).toBe(true);
            expect(result.results).toHaveLength(1);
            expect(result.results[0].success).toBe(true);
            expect(mockToolRegistryInstance.executeTool).toHaveBeenCalledWith('write_file', { path: 'test.txt', content: 'test' });
        });
        it('should handle tool execution failure', async () => {
            const mockToolResult = {
                success: false,
                error: 'Permission denied',
            };
            mockToolRegistryInstance.executeTool.mockResolvedValue(mockToolResult);
            const result = await agentService.executePlan();
            expect(result.success).toBe(false);
            expect(result.results).toHaveLength(1);
            expect(result.results[0].success).toBe(false);
            expect(result.results[0].error).toBe('Permission denied');
        });
        it('should fail when no plan is available', async () => {
            agentService.currentPlan = undefined;
            const result = await agentService.executePlan();
            expect(result.success).toBe(false);
            expect(result.error).toBe('No plan available for execution');
        });
        it('should fail when plan requires confirmation', async () => {
            agentService.currentState.isWaitingForConfirmation = true;
            const result = await agentService.executePlan();
            expect(result.success).toBe(false);
            expect(result.error).toBe('Plan requires confirmation before execution');
        });
    });
    describe('validatePlan', () => {
        it('should validate a correct plan', () => {
            const validPlan = {
                id: 'plan-1',
                title: 'Valid Plan',
                description: 'A valid plan',
                requires_confirmation: true,
                steps: [
                    {
                        id: 'step-1',
                        title: 'Valid Step',
                        description: 'A valid step',
                        tool: 'write_file',
                        parameters: { path: 'test.txt', content: 'test' },
                    },
                ],
            };
            const errors = agentService.validatePlan(validPlan);
            expect(errors).toHaveLength(0);
        });
        it('should detect missing required fields', () => {
            const invalidPlan = {
                id: 'plan-1',
                // Missing title
                description: 'A plan without title',
                requires_confirmation: true,
                steps: [],
            };
            const errors = agentService.validatePlan(invalidPlan);
            expect(errors.length).toBeGreaterThan(0);
            expect(errors.some((error) => error.includes('title'))).toBe(true);
        });
        it('should detect invalid steps', () => {
            const invalidPlan = {
                id: 'plan-1',
                title: 'Plan with invalid step',
                description: 'A plan with invalid step',
                requires_confirmation: true,
                steps: [
                    {
                        id: 'step-1',
                        // Missing title
                        description: 'Step without title',
                        tool: 'write_file',
                        parameters: {},
                    },
                ],
            };
            const errors = agentService.validatePlan(invalidPlan);
            expect(errors.length).toBeGreaterThan(0);
        });
    });
    describe('getState', () => {
        it('should return current agent state', () => {
            const state = agentService.getState();
            expect(state).toBeDefined();
            expect(state.isProcessing).toBe(false);
            expect(state.isWaitingForConfirmation).toBe(false);
        });
    });
    describe('reset', () => {
        it('should reset agent state', () => {
            // Set some state
            agentService.currentPlan = { id: 'test' };
            agentService.currentState.isProcessing = true;
            agentService.reset();
            const state = agentService.getState();
            expect(state.isProcessing).toBe(false);
            expect(agentService.currentPlan).toBeUndefined();
        });
    });
});
//# sourceMappingURL=AgentService.test.js.map