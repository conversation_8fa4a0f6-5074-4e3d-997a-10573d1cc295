"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const index_1 = require("@shared/constants/index");
// Expose secure API to renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    // LLM Service
    llm: {
        chat: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.LLM_CHAT, request),
        stream: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.LLM_STREAM, request),
        stop: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.LLM_STOP),
    },
    // Agent Service
    agent: {
        plan: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.AGENT_PLAN, request),
        execute: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.AGENT_EXECUTE, request),
        stop: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.AGENT_STOP),
        confirm: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.AGENT_CONFIRM, request),
    },
    // Tool System
    tools: {
        list: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.TOOL_LIST),
        execute: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.TOOL_EXECUTE, request),
    },
    // Conversations
    conversations: {
        create: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.CONVERSATION_CREATE, request),
        update: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.CONVERSATION_UPDATE, request),
        delete: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.CONVERSATION_DELETE, request),
        list: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.CONVERSATION_LIST),
        get: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.CONVERSATION_GET, request),
    },
    // Settings
    settings: {
        get: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.SETTINGS_GET),
        set: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.SETTINGS_SET, request),
        reset: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.SETTINGS_RESET),
    },
    // File System
    fs: {
        read: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.FS_READ, request),
        write: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.FS_WRITE, request),
        list: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.FS_LIST, request),
        glob: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.FS_GLOB, request),
    },
    // System
    system: {
        info: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.SYSTEM_INFO),
        shell: (request) => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.SYSTEM_SHELL, request),
    },
    // Window Management
    window: {
        minimize: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.WINDOW_MINIMIZE),
        maximize: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.WINDOW_MAXIMIZE),
        close: () => electron_1.ipcRenderer.invoke(index_1.IPC_CHANNELS.WINDOW_CLOSE),
    },
    // Event listeners
    on: (channel, callback) => {
        const validChannels = Object.values(index_1.IPC_CHANNELS);
        if (validChannels.includes(channel)) {
            electron_1.ipcRenderer.on(channel, (event, ...args) => callback(...args));
        }
    },
    removeAllListeners: (channel) => {
        const validChannels = Object.values(index_1.IPC_CHANNELS);
        if (validChannels.includes(channel)) {
            electron_1.ipcRenderer.removeAllListeners(channel);
        }
    },
});
//# sourceMappingURL=preload.js.map