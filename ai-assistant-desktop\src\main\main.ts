import { app, BrowserWindow, ipc<PERSON>ain, shell } from 'electron';
import * as path from 'path';
import { DatabaseService } from './database/DatabaseService';
import { SettingsService } from './services/SettingsService';
import { LLMService } from './services/LLMService';
import { ToolRegistry } from './services/ToolRegistry';
import { AgentService } from './services/AgentService';
import { FileSystemService } from './services/FileSystemService';
import { SystemService } from './services/SystemService';
import { IPC_CHANNELS } from '@shared/constants/index';

class MainApp {
  private mainWindow: BrowserWindow | null = null;
  private databaseService: DatabaseService;
  private settingsService: SettingsService;
  private llmService: LLMService;
  private toolRegistry: ToolRegistry;
  private agentService: AgentService;
  private fileSystemService: FileSystemService;
  private systemService: SystemService;

  constructor() {
    this.databaseService = new DatabaseService();
    this.settingsService = new SettingsService(this.databaseService);
    this.llmService = new LLMService();
    this.toolRegistry = new ToolRegistry();
    this.agentService = new AgentService(this.llmService, this.toolRegistry);
    this.fileSystemService = new FileSystemService();
    this.systemService = new SystemService();
  }

  async initialize() {
    await this.databaseService.initialize();
    await this.settingsService.initialize();
    await this.toolRegistry.initialize();
    this.setupIPC();
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
      titleBarStyle: 'hiddenInset',
      frame: false,
      show: false,
      backgroundColor: '#18181b',
    });

    // Load the app
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show();
        this.mainWindow.focus();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  setupIPC() {
    // LLM Service
    ipcMain.handle(IPC_CHANNELS.LLM_CHAT, async (event, request) => {
      try {
        const settings = await this.settingsService.getSettings();
        return await this.llmService.chat(request, settings.llm);
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.LLM_STREAM, async (event, request) => {
      try {
        const settings = await this.settingsService.getSettings();
        return await this.llmService.streamChat(request, settings.llm);
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // Agent Service
    ipcMain.handle(IPC_CHANNELS.AGENT_PLAN, async (event, request) => {
      try {
        return await this.agentService.createPlan(request);
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.AGENT_EXECUTE, async (event, request) => {
      try {
        return await this.agentService.executePlan(request);
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // Tool Registry
    ipcMain.handle(IPC_CHANNELS.TOOL_LIST, async () => {
      try {
        return { success: true, data: this.toolRegistry.getAvailableTools() };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.TOOL_EXECUTE, async (event, request) => {
      try {
        return await this.toolRegistry.executeTool(request.name, request.arguments);
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // Settings Service
    ipcMain.handle(IPC_CHANNELS.SETTINGS_GET, async () => {
      try {
        const settings = await this.settingsService.getSettings();
        return { success: true, data: settings };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.SETTINGS_SET, async (event, request) => {
      try {
        await this.settingsService.updateSettings(request);
        return { success: true };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // File System Service
    ipcMain.handle(IPC_CHANNELS.FS_READ, async (event, request) => {
      try {
        const content = await this.fileSystemService.readFile(request.path);
        return { success: true, data: content };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.FS_WRITE, async (event, request) => {
      try {
        await this.fileSystemService.writeFile(request.path, request.content);
        return { success: true };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.FS_LIST, async (event, request) => {
      try {
        const files = await this.fileSystemService.listDirectory(request.path);
        return { success: true, data: files };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // System Service
    ipcMain.handle(IPC_CHANNELS.SYSTEM_INFO, async () => {
      try {
        const info = await this.systemService.getSystemInfo();
        return { success: true, data: info };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.SYSTEM_SHELL, async (event, request) => {
      try {
        const result = await this.systemService.executeCommand(request.command, request.options);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // Conversation Management
    ipcMain.handle(IPC_CHANNELS.CONVERSATION_CREATE, async (event, request) => {
      try {
        const conversation = await this.databaseService.createConversation(request);
        return { success: true, data: conversation };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    ipcMain.handle(IPC_CHANNELS.CONVERSATION_LIST, async () => {
      try {
        const conversations = await this.databaseService.getConversations();
        return { success: true, data: conversations };
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // Window Management
    ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, () => {
      this.mainWindow?.close();
    });
  }

  async run() {
    // Handle app events
    app.whenReady().then(async () => {
      await this.initialize();
      this.createWindow();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', async () => {
      await this.databaseService.close();
    });

    // Security: Prevent new window creation
    app.on('web-contents-created', (event, contents) => {
      contents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });
    });
  }
}

// Create and run the app
const mainApp = new MainApp();
mainApp.run().catch(console.error);