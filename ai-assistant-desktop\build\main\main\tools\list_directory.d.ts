import type { ToolSchema, FileInfo } from '@shared/types';
export declare const category: "file_system";
export declare const schema: ToolSchema;
export declare function execute(args: {
    path: string;
    recursive?: boolean;
    includeHidden?: boolean;
    maxDepth?: number;
    sortBy?: 'name' | 'size' | 'modified' | 'type';
}): Promise<{
    path: string;
    absolutePath: string;
    files: FileInfo[];
    directories: FileInfo[];
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
}>;
export declare function getDirectoryTree(args: {
    path: string;
    maxDepth?: number;
    includeHidden?: boolean;
}): Promise<{
    name: string;
    path: string;
    type: 'directory' | 'file';
    children?: any[];
    size?: number;
    modified?: Date;
}>;
//# sourceMappingURL=list_directory.d.ts.map