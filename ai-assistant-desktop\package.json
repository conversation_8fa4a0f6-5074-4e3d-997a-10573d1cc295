{"name": "ai-assistant-desktop", "version": "1.0.0", "description": "A modern AI assistant desktop app with multi-LLM provider support and extensible tooling system", "main": "build/main/main.js", "homepage": "./", "author": "Your Name", "license": "MIT", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "concurrently \"npm run build:watch\" \"npm run electron\"", "dev": "concurrently \"npm run build:watch\" \"wait-on build/main/main.js && npm run electron\"", "electron": "electron .", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "webpack --config webpack.config.js", "build:watch": "concurrently \"npm run build:main:watch\" \"npm run build:renderer:watch\"", "build:main:watch": "tsc -p tsconfig.main.json --watch", "build:renderer:watch": "webpack --config webpack.config.js --watch", "build:prod": "cross-env NODE_ENV=production npm run build", "dist": "npm run build:prod && electron-builder", "dist:win": "npm run build:prod && electron-builder --win", "dist:mac": "npm run build:prod && electron-builder --mac", "dist:linux": "npm run build:prod && electron-builder --linux", "postinstall": "electron-builder install-app-deps", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"ajv": "^8.12.0", "better-sqlite3": "^12.3.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "diff2html": "^3.4.45", "glob": "^10.3.10", "mime-types": "^2.1.35", "node-fetch": "^2.7.0", "tiktoken": "^1.0.12", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"@eslint/js": "^8.56.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/better-sqlite3": "^7.6.8", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.11", "@types/mime-types": "^2.1.4", "@types/node": "^20.11.5", "@types/node-fetch": "^2.6.13", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "autoprefixer": "^10.4.17", "clsx": "^2.1.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "css-loader": "^6.9.1", "electron": "^28.2.0", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "framer-motion": "^11.0.3", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "lucide-react": "^0.323.0", "postcss": "^8.4.33", "postcss-loader": "^8.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.5.0", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.6", "style-loader": "^3.3.4", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "wait-on": "^7.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "zustand": "^4.4.7"}, "build": {"appId": "com.aiassistant.desktop", "productName": "AI Assistant <PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["build/**/*", "package.json", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}