"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = exports.category = void 0;
exports.execute = execute;
exports.searchText = searchText;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const fs_1 = require("fs");
const readline_1 = require("readline");
const constants_1 = require("@shared/constants");
exports.category = constants_1.TOOL_CATEGORIES.SEARCH;
exports.schema = {
    name: 'grep',
    description: 'Search for patterns in files using regular expressions. Returns matching lines with context.',
    category: constants_1.TOOL_CATEGORIES.SEARCH,
    parameters: {
        type: 'object',
        properties: {
            pattern: {
                type: 'string',
                description: 'The regular expression pattern to search for',
            },
            path: {
                type: 'string',
                description: 'File or directory path to search in',
            },
            recursive: {
                type: 'boolean',
                description: 'Search recursively in directories (default: false)',
                default: false,
            },
            caseSensitive: {
                type: 'boolean',
                description: 'Case-sensitive search (default: false)',
                default: false,
            },
            contextLines: {
                type: 'number',
                description: 'Number of context lines to show around matches (default: 0)',
                default: 0,
            },
            maxMatches: {
                type: 'number',
                description: 'Maximum number of matches to return per file (default: 100)',
                default: 100,
            },
            filePattern: {
                type: 'string',
                description: 'File name pattern to include (glob pattern)',
            },
            excludePattern: {
                type: 'string',
                description: 'File name pattern to exclude (glob pattern)',
            },
        },
        required: ['pattern', 'path'],
    },
    examples: [
        {
            description: 'Search for function definitions',
            arguments: { pattern: 'function\\s+\\w+', path: './src' },
            expectedOutput: 'List of matching lines with function definitions',
        },
    ],
    riskLevel: 'low',
};
async function execute(args) {
    const { pattern, path: searchPath, recursive = false, caseSensitive = false, contextLines = 0, maxMatches = 100, filePattern, excludePattern, } = args;
    const startTime = Date.now();
    const matches = [];
    let filesSearched = 0;
    let totalMatches = 0;
    try {
        // Create regex pattern
        const flags = caseSensitive ? 'g' : 'gi';
        const regex = new RegExp(pattern, flags);
        const absolutePath = path.resolve(searchPath);
        const stats = await fs.stat(absolutePath);
        if (stats.isFile()) {
            // Search single file
            if (shouldSearchFile(absolutePath, filePattern, excludePattern)) {
                const fileMatches = await searchInFile(absolutePath, regex, contextLines, maxMatches);
                matches.push(...fileMatches);
                filesSearched = 1;
                totalMatches = fileMatches.length;
            }
        }
        else if (stats.isDirectory()) {
            // Search directory
            const result = await searchInDirectory(absolutePath, regex, recursive, contextLines, maxMatches, filePattern, excludePattern);
            matches.push(...result.matches);
            filesSearched = result.filesSearched;
            totalMatches = result.totalMatches;
        }
        else {
            throw new Error(`Invalid path: ${searchPath}`);
        }
        return {
            pattern,
            searchPath: absolutePath,
            matches,
            filesSearched,
            totalMatches,
            executionTime: Date.now() - startTime,
        };
    }
    catch (error) {
        if (error.code === 'ENOENT') {
            throw new Error(`Path not found: ${searchPath}`);
        }
        else if (error.code === 'EACCES') {
            throw new Error(`Permission denied: ${searchPath}`);
        }
        else {
            throw new Error(`Search error: ${error.message}`);
        }
    }
}
async function searchInFile(filePath, regex, contextLines, maxMatches) {
    const matches = [];
    const lines = [];
    let lineNumber = 0;
    try {
        // Read file line by line
        const fileStream = (0, fs_1.createReadStream)(filePath);
        const rl = (0, readline_1.createInterface)({
            input: fileStream,
            crlfDelay: Infinity,
        });
        for await (const line of rl) {
            lineNumber++;
            lines.push(line);
            // Check if line matches pattern
            const lineMatches = Array.from(line.matchAll(regex));
            for (const match of lineMatches) {
                if (matches.length >= maxMatches) {
                    break;
                }
                const matchResult = {
                    file: filePath,
                    line: lineNumber,
                    column: (match.index || 0) + 1,
                    match: match[0],
                };
                // Add context if requested
                if (contextLines > 0) {
                    const startLine = Math.max(0, lineNumber - contextLines - 1);
                    const endLine = Math.min(lines.length - 1, lineNumber + contextLines - 1);
                    matchResult.context = {
                        before: lines.slice(startLine, lineNumber - 1),
                        after: [], // Will be filled as we read more lines
                    };
                }
                matches.push(matchResult);
            }
            if (matches.length >= maxMatches) {
                break;
            }
        }
        // Fill in the 'after' context for matches
        if (contextLines > 0) {
            for (const match of matches) {
                if (match.context) {
                    const startAfter = match.line;
                    const endAfter = Math.min(lines.length, match.line + contextLines);
                    match.context.after = lines.slice(startAfter, endAfter);
                }
            }
        }
        return matches;
    }
    catch (error) {
        if (error.code === 'EISDIR') {
            return []; // Skip directories
        }
        throw new Error(`Error searching file ${filePath}: ${error.message}`);
    }
}
async function searchInDirectory(dirPath, regex, recursive, contextLines, maxMatches, filePattern, excludePattern) {
    const allMatches = [];
    let filesSearched = 0;
    let totalMatches = 0;
    async function searchRecursively(currentDir, depth = 0) {
        // Prevent infinite recursion
        if (depth > 10)
            return;
        try {
            const entries = await fs.readdir(currentDir, { withFileTypes: true });
            for (const entry of entries) {
                if (totalMatches >= maxMatches * 10) {
                    // Limit total matches across all files
                    break;
                }
                const fullPath = path.join(currentDir, entry.name);
                if (entry.isDirectory()) {
                    if (recursive && !entry.name.startsWith('.')) {
                        await searchRecursively(fullPath, depth + 1);
                    }
                }
                else if (entry.isFile()) {
                    if (shouldSearchFile(fullPath, filePattern, excludePattern)) {
                        try {
                            const fileMatches = await searchInFile(fullPath, regex, contextLines, maxMatches);
                            allMatches.push(...fileMatches);
                            filesSearched++;
                            totalMatches += fileMatches.length;
                        }
                        catch (error) {
                            console.warn(`Skipping file ${fullPath}: ${error}`);
                        }
                    }
                }
            }
        }
        catch (error) {
            console.warn(`Error accessing directory ${currentDir}: ${error}`);
        }
    }
    await searchRecursively(dirPath);
    return {
        matches: allMatches.slice(0, maxMatches * 10), // Limit total results
        filesSearched,
        totalMatches,
    };
}
function shouldSearchFile(filePath, includePattern, excludePattern) {
    const fileName = path.basename(filePath);
    // Skip binary files and common non-text extensions
    const binaryExtensions = ['.exe', '.dll', '.so', '.dylib', '.bin', '.jpg', '.png', '.gif', '.pdf', '.zip', '.tar', '.gz'];
    const ext = path.extname(fileName).toLowerCase();
    if (binaryExtensions.includes(ext)) {
        return false;
    }
    // Check include pattern
    if (includePattern) {
        const includeRegex = new RegExp(includePattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
        if (!includeRegex.test(fileName)) {
            return false;
        }
    }
    // Check exclude pattern
    if (excludePattern) {
        const excludeRegex = new RegExp(excludePattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
        if (excludeRegex.test(fileName)) {
            return false;
        }
    }
    // Skip hidden files and common ignore patterns
    if (fileName.startsWith('.') || fileName.includes('node_modules') || fileName.includes('.git')) {
        return false;
    }
    return true;
}
// Helper function for simple text search (non-regex)
async function searchText(args) {
    const { text, ...otherArgs } = args;
    // Escape special regex characters for literal text search
    const escapedText = text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    return execute({
        pattern: escapedText,
        ...otherArgs,
    });
}
//# sourceMappingURL=grep.js.map